package com.mdsadrulhasan.gogolaundry.viewmodel;

import android.app.Application;
import android.location.Location;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MediatorLiveData;
import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.Transformations;

import com.mdsadrulhasan.gogolaundry.data.Resource;
import com.mdsadrulhasan.gogolaundry.database.entity.LaundryShopEntity;
import com.mdsadrulhasan.gogolaundry.model.ShopFilter;
import com.mdsadrulhasan.gogolaundry.repository.LaundryShopRepository;
import com.mdsadrulhasan.gogolaundry.repository.LocationRepository;

import java.util.List;

/**
 * ViewModel for Shop Map Fragment
 */
public class ShopMapViewModel extends AndroidViewModel {

    private static final String TAG = "ShopMapViewModel";

    private final LaundryShopRepository shopRepository;
    private final LocationRepository locationRepository;

    // LiveData for shops
    private final MediatorLiveData<List<LaundryShopEntity>> shops = new MediatorLiveData<>();
    private final MutableLiveData<Boolean> isLoading = new MutableLiveData<>(false);
    private final MutableLiveData<String> errorMessage = new MutableLiveData<>();

    // Current location
    private final MutableLiveData<Location> currentLocation = new MutableLiveData<>();

    // Search and filter parameters
    private final MutableLiveData<String> searchQuery = new MutableLiveData<>();
    private final MutableLiveData<Double> searchRadius = new MutableLiveData<>(20.0); // Default 20km
    private final MutableLiveData<Integer> selectedServiceFilter = new MutableLiveData<>();
    private final MutableLiveData<ShopFilter> currentFilter = new MutableLiveData<>(new ShopFilter());

    // Map state
    private final MutableLiveData<Boolean> isMapReady = new MutableLiveData<>(false);
    private final MutableLiveData<Boolean> showShopList = new MutableLiveData<>(false);

    public ShopMapViewModel(@NonNull Application application) {
        super(application);

        shopRepository = LaundryShopRepository.getInstance();
        locationRepository = LocationRepository.getInstance(application);

        // Initialize with empty list
        shops.setValue(null);
        isLoading.setValue(false);
    }

    /**
     * Load all shops
     */
    public void loadAllShops() {
        Log.d(TAG, "loadAllShops() called");
        isLoading.setValue(true);
        errorMessage.setValue(null);

        LiveData<Resource<List<LaundryShopEntity>>> source = shopRepository.getAllShops(true); // Force refresh
        shops.addSource(source, resource -> {
            if (resource != null) {
                Log.d(TAG, "loadAllShops() - Received resource with status: " + resource.status);
                switch (resource.status) {
                    case SUCCESS:
                        isLoading.setValue(false);
                        Log.d(TAG, "loadAllShops() - SUCCESS: Received " + (resource.data != null ? resource.data.size() : 0) + " shops");
                        shops.setValue(resource.data);
                        break;
                    case ERROR:
                        isLoading.setValue(false);
                        Log.e(TAG, "loadAllShops() - ERROR: " + resource.message);
                        errorMessage.setValue(resource.message);
                        break;
                    case LOADING:
                        isLoading.setValue(true);
                        Log.d(TAG, "loadAllShops() - LOADING");
                        break;
                }
            } else {
                Log.w(TAG, "loadAllShops() - Received null resource");
            }
        });
    }

    /**
     * Load nearby shops within radius
     */
    public void loadNearbyShops(double latitude, double longitude, double radiusKm) {
        Log.d(TAG, "loadNearbyShops() called with lat: " + latitude + ", lng: " + longitude + ", radius: " + radiusKm + "km");
        isLoading.setValue(true);
        errorMessage.setValue(null);

        // Update current location
        Location location = new Location("current");
        location.setLatitude(latitude);
        location.setLongitude(longitude);
        currentLocation.setValue(location);

        // Update search radius
        searchRadius.setValue(radiusKm);

        LiveData<Resource<List<LaundryShopEntity>>> source = shopRepository.getNearbyShops(latitude, longitude, radiusKm);
        shops.addSource(source, resource -> {
            if (resource != null) {
                Log.d(TAG, "loadNearbyShops() - Received resource with status: " + resource.status);
                switch (resource.status) {
                    case SUCCESS:
                        isLoading.setValue(false);
                        Log.d(TAG, "loadNearbyShops() - SUCCESS: Received " + (resource.data != null ? resource.data.size() : 0) + " nearby shops");
                        shops.setValue(resource.data);
                        break;
                    case ERROR:
                        isLoading.setValue(false);
                        Log.e(TAG, "loadNearbyShops() - ERROR: " + resource.message);
                        errorMessage.setValue(resource.message);
                        break;
                    case LOADING:
                        isLoading.setValue(true);
                        Log.d(TAG, "loadNearbyShops() - LOADING");
                        break;
                }
            } else {
                Log.w(TAG, "loadNearbyShops() - Received null resource");
            }
        });
    }

    /**
     * Search shops by query
     */
    public void searchShops(String query) {
        Log.d(TAG, "searchShops() called with query: '" + query + "'");

        if (query == null || query.trim().isEmpty()) {
            Log.d(TAG, "Empty search query, loading all shops");
            loadAllShops();
            return;
        }

        String trimmedQuery = query.trim();
        Log.d(TAG, "Starting search for: '" + trimmedQuery + "'");

        isLoading.setValue(true);
        errorMessage.setValue(null);
        searchQuery.setValue(trimmedQuery);

        LiveData<Resource<List<LaundryShopEntity>>> source = shopRepository.searchShops(trimmedQuery);
        shops.addSource(source, resource -> {
            if (resource != null) {
                Log.d(TAG, "Search result received with status: " + resource.status);
                switch (resource.status) {
                    case SUCCESS:
                        isLoading.setValue(false);
                        List<LaundryShopEntity> searchResults = resource.data;
                        int resultCount = searchResults != null ? searchResults.size() : 0;
                        Log.d(TAG, "Search SUCCESS: Found " + resultCount + " shops for query '" + trimmedQuery + "'");

                        if (searchResults != null && !searchResults.isEmpty()) {
                            Log.d(TAG, "First few search results:");
                            for (int i = 0; i < Math.min(3, searchResults.size()); i++) {
                                LaundryShopEntity shop = searchResults.get(i);
                                Log.d(TAG, "  " + (i+1) + ". " + shop.getName() + " (ID: " + shop.getId() + ", Rating: " + shop.getRating() + ")");
                            }
                        } else {
                            Log.w(TAG, "No shops found for search query: '" + trimmedQuery + "'");
                        }

                        shops.setValue(searchResults);
                        break;
                    case ERROR:
                        isLoading.setValue(false);
                        Log.e(TAG, "Search ERROR: " + resource.message + " for query '" + trimmedQuery + "'");
                        errorMessage.setValue(resource.message);
                        break;
                    case LOADING:
                        isLoading.setValue(true);
                        Log.d(TAG, "Search LOADING for query '" + trimmedQuery + "'");
                        break;
                }
            } else {
                Log.w(TAG, "Received null resource for search query: '" + trimmedQuery + "'");
            }
        });
    }

    /**
     * Filter shops by location
     */
    public void filterShopsByLocation(Integer divisionId, Integer districtId, Integer upazillaId) {
        isLoading.setValue(true);
        errorMessage.setValue(null);

        LiveData<Resource<List<LaundryShopEntity>>> source = shopRepository.getShopsByLocation(divisionId, districtId, upazillaId);
        shops.addSource(source, resource -> {
            if (resource != null) {
                switch (resource.status) {
                    case SUCCESS:
                        isLoading.setValue(false);
                        shops.setValue(resource.data);
                        break;
                    case ERROR:
                        isLoading.setValue(false);
                        errorMessage.setValue(resource.message);
                        break;
                    case LOADING:
                        isLoading.setValue(true);
                        break;
                }
            }
        });
    }

    /**
     * Get current location from location repository
     */
    public void getCurrentLocation() {
        LiveData<Resource<Location>> locationSource = locationRepository.getCurrentLocation();

        // Create a temporary mediator to handle the location result
        MediatorLiveData<Resource<Location>> mediator = new MediatorLiveData<>();
        mediator.addSource(locationSource, resource -> {
            if (resource != null && resource.status == Resource.Status.SUCCESS && resource.data != null) {
                currentLocation.setValue(resource.data);
                // Load nearby shops with current location
                loadNearbyShops(resource.data.getLatitude(), resource.data.getLongitude(), searchRadius.getValue());
            } else if (resource != null && resource.status == Resource.Status.ERROR) {
                errorMessage.setValue(resource.message);
                // Load all shops as fallback
                loadAllShops();
            }
        });
    }

    /**
     * Refresh shops data
     */
    public void refreshShops() {
        Location location = currentLocation.getValue();
        if (location != null) {
            loadNearbyShops(location.getLatitude(), location.getLongitude(), searchRadius.getValue());
        } else {
            loadAllShops();
        }
    }

    /**
     * Update search radius
     */
    public void updateSearchRadius(double radiusKm) {
        searchRadius.setValue(radiusKm);
        Location location = currentLocation.getValue();
        if (location != null) {
            loadNearbyShops(location.getLatitude(), location.getLongitude(), radiusKm);
        }
    }

    /**
     * Apply filter to shops
     */
    public void applyFilter(ShopFilter filter) {
        Log.d(TAG, "applyFilter() called with filter: " + filter.toString());
        currentFilter.setValue(filter);

        // Update search radius from filter
        searchRadius.setValue(filter.getMaxDistance());
        Log.d(TAG, "Updated search radius to: " + filter.getMaxDistance() + " km");

        // Apply the filter based on current context
        if (filter.getSearchQuery() != null && !filter.getSearchQuery().trim().isEmpty()) {
            // Search with filter
            Log.d(TAG, "Applying search filter with query: '" + filter.getSearchQuery() + "'");
            searchShopsWithFilter(filter.getSearchQuery(), filter);
        } else if (filter.hasLocationFilter()) {
            // Location-based filter
            Log.d(TAG, "Applying location filter - Division: " + filter.getDivisionId() +
                      ", District: " + filter.getDistrictId() + ", Upazilla: " + filter.getUpazillaId());
            filterShopsByLocation(filter.getDivisionId(), filter.getDistrictId(), filter.getUpazillaId());
        } else {
            // Nearby shops with filter
            Location location = currentLocation.getValue();
            if (location != null) {
                Log.d(TAG, "Applying nearby shops filter at location: " + location.getLatitude() + ", " + location.getLongitude());
                loadNearbyShopsWithFilter(location.getLatitude(), location.getLongitude(), filter);
            } else {
                Log.d(TAG, "No location available, applying filter to all shops");
                loadAllShopsWithFilter(filter);
            }
        }
    }

    /**
     * Search shops with filter applied
     */
    private void searchShopsWithFilter(String query, ShopFilter filter) {
        isLoading.setValue(true);
        errorMessage.setValue(null);
        searchQuery.setValue(query);

        LiveData<Resource<List<LaundryShopEntity>>> source = shopRepository.searchShops(query);
        shops.addSource(source, resource -> {
            if (resource != null) {
                switch (resource.status) {
                    case SUCCESS:
                        isLoading.setValue(false);
                        List<LaundryShopEntity> filteredShops = applyLocalFilter(resource.data, filter);
                        shops.setValue(filteredShops);
                        break;
                    case ERROR:
                        isLoading.setValue(false);
                        errorMessage.setValue(resource.message);
                        break;
                    case LOADING:
                        isLoading.setValue(true);
                        break;
                }
            }
        });
    }

    /**
     * Load nearby shops with filter applied
     */
    private void loadNearbyShopsWithFilter(double latitude, double longitude, ShopFilter filter) {
        isLoading.setValue(true);
        errorMessage.setValue(null);

        LiveData<Resource<List<LaundryShopEntity>>> source = shopRepository.getNearbyShops(latitude, longitude, filter.getMaxDistance());
        shops.addSource(source, resource -> {
            if (resource != null) {
                switch (resource.status) {
                    case SUCCESS:
                        isLoading.setValue(false);
                        List<LaundryShopEntity> filteredShops = applyLocalFilter(resource.data, filter);
                        shops.setValue(filteredShops);
                        break;
                    case ERROR:
                        isLoading.setValue(false);
                        errorMessage.setValue(resource.message);
                        break;
                    case LOADING:
                        isLoading.setValue(true);
                        break;
                }
            }
        });
    }

    /**
     * Load all shops with filter applied
     */
    private void loadAllShopsWithFilter(ShopFilter filter) {
        isLoading.setValue(true);
        errorMessage.setValue(null);

        LiveData<Resource<List<LaundryShopEntity>>> source = shopRepository.getAllShops(false);
        shops.addSource(source, resource -> {
            if (resource != null) {
                switch (resource.status) {
                    case SUCCESS:
                        isLoading.setValue(false);
                        List<LaundryShopEntity> filteredShops = applyLocalFilter(resource.data, filter);
                        shops.setValue(filteredShops);
                        break;
                    case ERROR:
                        isLoading.setValue(false);
                        errorMessage.setValue(resource.message);
                        break;
                    case LOADING:
                        isLoading.setValue(true);
                        break;
                }
            }
        });
    }

    /**
     * Apply local filter to shop list
     */
    private List<LaundryShopEntity> applyLocalFilter(List<LaundryShopEntity> shops, ShopFilter filter) {
        if (shops == null || shops.isEmpty()) {
            Log.d(TAG, "applyLocalFilter: No shops to filter (null or empty list)");
            return shops;
        }

        int originalCount = shops.size();
        Log.d(TAG, "applyLocalFilter: Filtering " + originalCount + " shops with filter: " + filter.toString());

        List<LaundryShopEntity> filteredShops = shops.stream()
                .filter(shop -> {
                    // Rating filter
                    if (filter.getMinRating() > 0 && shop.getRating() < filter.getMinRating()) {
                        Log.v(TAG, "Filtered out shop '" + shop.getName() + "' - Rating " + shop.getRating() + " < " + filter.getMinRating());
                        return false;
                    }

                    // Verified filter
                    if (filter.isVerifiedOnly() && !shop.isVerified()) {
                        Log.v(TAG, "Filtered out shop '" + shop.getName() + "' - Not verified");
                        return false;
                    }

                    // Active filter
                    if (filter.isActiveOnly() && !shop.isActive()) {
                        Log.v(TAG, "Filtered out shop '" + shop.getName() + "' - Not active");
                        return false;
                    }

                    // Open now filter (simplified - would need proper time checking)
                    if (filter.isOpenNowOnly()) {
                        // TODO: Implement proper operating hours check
                        // For now, assume all shops are open
                        Log.v(TAG, "Open now filter applied to shop '" + shop.getName() + "' - Assuming open");
                    }

                    return true;
                })
                .collect(java.util.stream.Collectors.toList());

        int filteredCount = filteredShops.size();
        Log.d(TAG, "applyLocalFilter: Filtered from " + originalCount + " to " + filteredCount + " shops");

        if (filteredCount > 0) {
            Log.d(TAG, "First few filtered results:");
            for (int i = 0; i < Math.min(3, filteredCount); i++) {
                LaundryShopEntity shop = filteredShops.get(i);
                Log.d(TAG, "  " + (i+1) + ". " + shop.getName() + " (Rating: " + shop.getRating() +
                          ", Verified: " + shop.isVerified() + ", Active: " + shop.isActive() + ")");
            }
        }

        return filteredShops;
    }

    /**
     * Clear all filters
     */
    public void clearFilters() {
        ShopFilter defaultFilter = new ShopFilter();
        currentFilter.setValue(defaultFilter);
        applyFilter(defaultFilter);
    }

    // Getters for LiveData

    public LiveData<List<LaundryShopEntity>> getShops() {
        return shops;
    }

    public LiveData<Boolean> getIsLoading() {
        return isLoading;
    }

    public LiveData<String> getErrorMessage() {
        return errorMessage;
    }

    public LiveData<Location> getCurrentLocationLiveData() {
        return currentLocation;
    }

    public LiveData<String> getSearchQuery() {
        return searchQuery;
    }

    public LiveData<Double> getSearchRadius() {
        return searchRadius;
    }

    public LiveData<Boolean> getIsMapReady() {
        return isMapReady;
    }

    public LiveData<Boolean> getShowShopList() {
        return showShopList;
    }

    public LiveData<ShopFilter> getCurrentFilter() {
        return currentFilter;
    }

    // Setters for UI state

    public void setMapReady(boolean ready) {
        isMapReady.setValue(ready);
    }

    public void toggleShopList() {
        Boolean current = showShopList.getValue();
        showShopList.setValue(current == null ? true : !current);
    }

    public void setShowShopList(boolean show) {
        showShopList.setValue(show);
    }

    public void clearErrorMessage() {
        errorMessage.setValue(null);
    }

    /**
     * Check if location permission is available
     */
    public boolean hasLocationPermission() {
        return locationRepository.hasLocationPermission();
    }
}
