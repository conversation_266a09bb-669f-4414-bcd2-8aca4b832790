package com.mdsadrulhasan.gogolaundry.api;

import android.content.Context;
import android.util.Log;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import java.util.concurrent.TimeUnit;

import okhttp3.OkHttpClient;
import okhttp3.logging.HttpLoggingInterceptor;
import retrofit2.Retrofit;
import retrofit2.converter.gson.GsonConverterFactory;

/**
 * API Client for making network requests
 */
public class ApiClient {
    private static final String TAG = "ApiClient";
    //private static final String BASE_URL = "http://*************/kosbazillaonlineseba/api/";
    // Try localhost first, then fallback to IP if needed
    private static final String BASE_URL = "http://********/GoGoLaundry/GoGoLaundryAdminPanel/api/";
    // private static final String BASE_URL = "http://*************/GoGoLaundry/GoGoLaundryAdminPanel/api/";
    // private static final String BASE_URL = "http://*************/otp_management/api/";
    private static final int TIMEOUT = 30; // seconds

    private static OkHttpClient okHttpClient = null;
    private static Retrofit retrofit = null;
    private static ApiService apiService = null;
    private static CookieInterceptor cookieInterceptor = null;
    private static PersistentCookieJar cookieJar = null;

    /**
     * Get OkHttpClient instance
     *
     * @param context Application context
     * @return OkHttpClient instance
     */
    public static synchronized OkHttpClient getOkHttpClient(Context context) {
        if (okHttpClient == null) {
            Log.d(TAG, "Creating new OkHttpClient instance");

            // Create cookie jar if not already created
            if (cookieJar == null) {
                cookieJar = new PersistentCookieJar(context.getApplicationContext());
            }

            // Create cookie interceptor if not already created
            if (cookieInterceptor == null) {
                cookieInterceptor = new CookieInterceptor(context.getApplicationContext());
            }

            // Create OkHttpClient with logging
            OkHttpClient.Builder httpClient = new OkHttpClient.Builder()
                    .connectTimeout(TIMEOUT, TimeUnit.SECONDS)
                    .readTimeout(TIMEOUT, TimeUnit.SECONDS)
                    .writeTimeout(TIMEOUT, TimeUnit.SECONDS)
                    .cookieJar(cookieJar);

            // Add logging interceptor (always enabled for development)
            HttpLoggingInterceptor logging = new HttpLoggingInterceptor();
            logging.setLevel(HttpLoggingInterceptor.Level.BODY);
            httpClient.addInterceptor(logging);

            // Add cookie interceptor for session management
            httpClient.addInterceptor(cookieInterceptor);

            // Build OkHttpClient
            okHttpClient = httpClient.build();
        }

        return okHttpClient;
    }

    /**
     * Get Retrofit instance
     *
     * @param context Application context
     * @return Retrofit instance
     */
    public static synchronized Retrofit getClient(Context context) {
        if (retrofit == null) {
            // Log the base URL for debugging
            Log.d(TAG, "Initializing Retrofit with base URL: " + BASE_URL);

            // Get OkHttpClient
            OkHttpClient client = getOkHttpClient(context);

            // Create a Gson instance with lenient parsing
            Gson gson = new GsonBuilder()
                    .setLenient()
                    .create();

            // Build Retrofit with lenient Gson
            retrofit = new Retrofit.Builder()
                    .baseUrl(BASE_URL)
                    .addConverterFactory(GsonConverterFactory.create(gson))
                    .client(client)
                    .build();

            Log.d(TAG, "Retrofit initialized successfully");
        }

        return retrofit;
    }

    /**
     * Get API service
     *
     * @param context Application context
     * @return API service
     */
    public static synchronized ApiService getApiService(Context context) {
        if (apiService == null) {
            apiService = getClient(context).create(ApiService.class);
            Log.d(TAG, "ApiService created");
        }

        return apiService;
    }

    /**
     * Reset API client (use after logout)
     */
    public static synchronized void resetApiClient() {
        Log.d(TAG, "Resetting API client");

        // Clear cookies if cookie jar exists
        if (cookieJar != null) {
            cookieJar.clear();
        }

        okHttpClient = null;
        retrofit = null;
        apiService = null;
        cookieInterceptor = null;
        cookieJar = null;
    }

    /**
     * Force refresh API client
     * This method forces the API client to be recreated, which will cause it to fetch the latest configuration
     */
    public static synchronized void forceRefreshApiClient() {
        Log.d(TAG, "Forcing refresh of API client");

        // Reset the API client
        resetApiClient();

        // Clear the configuration cache in SessionManager
        // This will be done by the calling code
    }

    /**
     * Get base URL
     *
     * @return Base URL
     */
    public static String getBaseUrl() {
        return BASE_URL;
    }

    /**
     * Get Retrofit instance without context (for compatibility)
     * Uses application context from GoGoLaundryApp
     *
     * @return Retrofit instance
     */
    public static synchronized Retrofit getRetrofitInstance() {
        if (retrofit == null) {
            // Log the base URL for debugging
            Log.d(TAG, "Initializing Retrofit with base URL: " + BASE_URL);

            // Create a simple OkHttpClient for this method
            OkHttpClient.Builder httpClient = new OkHttpClient.Builder()
                    .connectTimeout(TIMEOUT, TimeUnit.SECONDS)
                    .readTimeout(TIMEOUT, TimeUnit.SECONDS)
                    .writeTimeout(TIMEOUT, TimeUnit.SECONDS);

            // Add logging interceptor
            HttpLoggingInterceptor logging = new HttpLoggingInterceptor();
            logging.setLevel(HttpLoggingInterceptor.Level.BODY);
            httpClient.addInterceptor(logging);

            // Create a Gson instance with lenient parsing
            Gson gson = new GsonBuilder()
                    .setLenient()
                    .create();

            // Build Retrofit with lenient Gson
            retrofit = new Retrofit.Builder()
                    .baseUrl(BASE_URL)
                    .addConverterFactory(GsonConverterFactory.create(gson))
                    .client(httpClient.build())
                    .build();

            Log.d(TAG, "Retrofit initialized successfully");
        }

        return retrofit;
    }
}
