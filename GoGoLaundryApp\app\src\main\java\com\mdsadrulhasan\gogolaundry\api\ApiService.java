package com.mdsadrulhasan.gogolaundry.api;

import com.mdsadrulhasan.gogolaundry.database.entity.LaundryShopEntity;
import com.mdsadrulhasan.gogolaundry.fcm.FCMTokenRequest;
import com.mdsadrulhasan.gogolaundry.model.DeliveryFeeResponse;
import com.mdsadrulhasan.gogolaundry.model.Item;
import com.mdsadrulhasan.gogolaundry.model.Notification;
import com.mdsadrulhasan.gogolaundry.model.Order;
import com.mdsadrulhasan.gogolaundry.model.OrderStatusHistory;
import com.mdsadrulhasan.gogolaundry.model.Service;

import java.util.List;

import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import retrofit2.Call;
import retrofit2.http.Field;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.GET;
import retrofit2.http.Multipart;
import retrofit2.http.POST;
import retrofit2.http.Part;
import retrofit2.http.Path;
import retrofit2.http.Query;

/**
 * API Service interface for Retrofit
 */
public interface ApiService {

    /**
     * Get base URL for API
     *
     * @return Base URL for API
     */
    default String getBaseUrl() {
        return "http://********/GoGoLaundry/GoGoLaundryAdminPanel/";
    }

    /**
     * Get app configuration
     *
     * @param appVersion Current app version
     * @return API response with configuration
     */
    @GET("config.php")
    Call<ApiResponse<AppConfig>> getConfig(@Query("app_version") String appVersion);

    /**
     * Get orders for a user
     *
     * @param userId User ID
     * @return API response with orders
     */
    @GET("orders/user.php")
    Call<ApiResponse<List<Order>>> getUserOrders(@Query("user_id") int userId);

    /**
     * Get order details
     *
     * @param orderId Order ID
     * @return API response with order details
     */
    @GET("orders/details.php/{orderId}")
    Call<ApiResponse<Order>> getOrderDetails(@Path("orderId") int orderId);

    /**
     * Create a new order
     *
     * @param userId User ID
     * @param items Order items (JSON string)
     * @param subtotal Subtotal amount
     * @param discount Discount amount
     * @param deliveryFee Delivery fee
     * @param total Total amount
     * @param paymentMethod Payment method
     * @param pickupAddress Pickup address
     * @param pickupDate Pickup date
     * @param pickupTimeSlot Pickup time slot
     * @param deliveryAddress Delivery address
     * @param notes Order notes
     * @param transactionId Transaction ID for mobile banking or card number for card payments (optional)
     * @param paymentProvider Payment provider (bKash, Nagad, Rocket, Card) (optional)
     * @return API response with created order
     */
    @FormUrlEncoded
    @POST("orders/create.php")
    Call<ApiResponse<Order>> createOrder(
            @Field("user_id") int userId,
            @Field("items") String items,
            @Field("subtotal") double subtotal,
            @Field("discount") double discount,
            @Field("delivery_fee") double deliveryFee,
            @Field("total") double total,
            @Field("payment_method") String paymentMethod,
            @Field("pickup_address") String pickupAddress,
            @Field("pickup_date") String pickupDate,
            @Field("pickup_time_slot") String pickupTimeSlot,
            @Field("delivery_address") String deliveryAddress,
            @Field("notes") String notes,
            @Field("transaction_id") String transactionId,
            @Field("payment_provider") String paymentProvider);

    /**
     * Send OTP for registration
     *
     * @param phone Phone number
     * @param email Email (optional)
     * @param purpose Purpose of OTP (registration, login, reset_password)
     * @return API response
     */
    @FormUrlEncoded
    @POST("send_otp.php")
    Call<Object> sendRegistrationOtp(
            @Field("phone") String phone,
            @Field("email") String email,
            @Field("purpose") String purpose);

    /**
     * Verify OTP
     *
     * @param phone Phone number
     * @param otp OTP code
     * @param purpose Purpose of OTP
     * @return API response
     */
    @FormUrlEncoded
    @POST("verify_otp.php")
    Call<Object> verifyOtp(
            @Field("phone") String phone,
            @Field("otp") String otp,
            @Field("purpose") String purpose);

    /**
     * Register user
     *
     * @param fullName Full name
     * @param phone Phone number
     * @param email Email (optional)
     * @param password Password
     * @param otp OTP code
     * @param address Address/Village (optional)
     * @param divisionId Division ID (optional)
     * @param districtId District ID (optional)
     * @param upazillaId Upazilla ID (optional)
     * @param divisionName Division name (optional)
     * @param districtName District name (optional)
     * @param upazillaName Upazilla name (optional)
     * @return API response with user data
     */
    @FormUrlEncoded
    @POST("register.php")
    Call<ApiResponse<UserResponse>> register(
            @Field("full_name") String fullName,
            @Field("phone") String phone,
            @Field("email") String email,
            @Field("password") String password,
            @Field("otp") String otp,
            @Field("address") String address,
            @Field("division_id") Integer divisionId,
            @Field("district_id") Integer districtId,
            @Field("upazilla_id") Integer upazillaId,
            @Field("division_name") String divisionName,
            @Field("district_name") String districtName,
            @Field("upazilla_name") String upazillaName);

    /**
     * Login with password
     *
     * @param phone Phone number
     * @param password Password
     * @return API response with user data
     */
    @FormUrlEncoded
    @POST("login.php")
    Call<ApiResponse<UserResponse>> login(
            @Field("phone") String phone,
            @Field("password") String password);

    /**
     * Reset password
     *
     * @param phone Phone number
     * @param otp OTP code
     * @param newPassword New password
     * @return API response
     */
    @FormUrlEncoded
    @POST("reset_password.php")
    Call<Object> resetPassword(
            @Field("phone") String phone,
            @Field("otp") String otp,
            @Field("new_password") String newPassword);

    /**
     * Upload profile picture
     *
     * @param profilePicture Profile picture file
     * @param userId User ID for authentication
     * @return API response with updated user data
     */
    @Multipart
    @POST("upload_profile_picture.php")
    Call<Object> uploadProfilePicture(
            @Part MultipartBody.Part profilePicture,
            @Part("user_id") RequestBody userId);

    /**
     * Change password
     *
     * @param currentPassword Current password
     * @param newPassword New password
     * @param userId User ID for authentication
     * @return API response
     */
    @FormUrlEncoded
    @POST("change_password.php")
    Call<Object> changePassword(
            @Field("current_password") String currentPassword,
            @Field("new_password") String newPassword,
            @Field("user_id") int userId);

    /**
     * Delete account (soft delete)
     *
     * @param confirmation Confirmation text (must be "DELETE")
     * @param userId User ID for authentication
     * @return API response
     */
    @FormUrlEncoded
    @POST("delete_account.php")
    Call<Object> deleteAccount(
            @Field("confirmation") String confirmation,
            @Field("user_id") int userId);

    /**
     * Verify user account status
     *
     * @param userId User ID
     * @param phone Phone number
     * @return API response with account status
     */
    @FormUrlEncoded
    @POST("verify_account_status.php")
    Call<ApiResponse<AccountStatusResponse>> verifyAccountStatus(
            @Field("user_id") int userId,
            @Field("phone") String phone);

    /**
     * Track order by tracking number
     *
     * @param trackingNumber Order tracking number
     * @return API response with order tracking response
     */
    @GET("orders/tracking.php")
    Call<ApiResponse<OrderTrackingResponse>> trackOrder(@Query("tracking_number") String trackingNumber);

    /**
     * Get order status history
     *
     * @param orderId Order ID
     * @return API response with order status history
     */
    @GET("orders/history.php/{orderId}")
    Call<ApiResponse<List<OrderStatusHistory>>> getOrderStatusHistory(@Path("orderId") int orderId);

    /**
     * Update payment status for an order
     *
     * @param orderId Order ID
     * @param paymentStatus New payment status
     * @param adminNotes Admin notes (optional)
     * @param updatedBy Admin ID who updated the status
     * @return API response with updated order
     */
    @FormUrlEncoded
    @POST("orders/update_payment_status.php")
    Call<ApiResponse<Order>> updatePaymentStatus(
            @Field("order_id") int orderId,
            @Field("payment_status") String paymentStatus,
            @Field("admin_notes") String adminNotes,
            @Field("updated_by") int updatedBy
    );

    /**
     * Reorder an existing order
     *
     * @param orderId Original order ID
     * @param userId User ID
     * @return API response with new order
     */
    @FormUrlEncoded
    @POST("orders/reorder.php")
    Call<ApiResponse<Order>> reorderExistingOrder(
            @Field("order_id") int orderId,
            @Field("user_id") int userId);

    /**
     * Get all services or active services
     *
     * @param activeOnly Whether to get only active services (1) or all services (0)
     * @return API response with services
     */
    @GET("services.php")
    Call<ApiResponse<List<Service>>> getServices(@Query("active_only") int activeOnly);

    /**
     * Get items, optionally filtered by service ID
     *
     * @param serviceId Service ID to filter by (optional)
     * @param activeOnly Whether to get only active items (1) or all items (0)
     * @return API response with items
     */
    @GET("items.php")
    Call<ApiResponse<List<Item>>> getItems(
            @Query("service_id") Integer serviceId,
            @Query("active_only") int activeOnly);

    /**
     * Get all laundry shops
     *
     * @param activeOnly Whether to get only active shops (1) or all shops (0)
     * @param verifiedOnly Whether to get only verified shops (1) or all shops (0)
     * @param limit Maximum number of shops to return
     * @param offset Number of shops to skip
     * @param divisionId Filter by division ID (optional)
     * @param districtId Filter by district ID (optional)
     * @param upazillaId Filter by upazilla ID (optional)
     * @return API response with shops
     */
    @GET("shops/list.php")
    Call<ApiResponse<List<LaundryShopEntity>>> getAllShops(
            @Query("active_only") int activeOnly,
            @Query("verified_only") int verifiedOnly,
            @Query("limit") int limit,
            @Query("offset") int offset,
            @Query("division_id") Integer divisionId,
            @Query("district_id") Integer districtId,
            @Query("upazilla_id") Integer upazillaId);

    /**
     * Search laundry shops by query
     *
     * @param query Search query
     * @param activeOnly Whether to get only active shops (1) or all shops (0)
     * @param verifiedOnly Whether to get only verified shops (1) or all shops (0)
     * @param limit Maximum number of shops to return
     * @param offset Number of shops to skip
     * @param divisionId Filter by division ID (optional)
     * @param districtId Filter by district ID (optional)
     * @param upazillaId Filter by upazilla ID (optional)
     * @return API response with shops
     */
    @GET("shops/search.php")
    Call<ApiResponse<List<LaundryShopEntity>>> searchShops(
            @Query("query") String query,
            @Query("active_only") int activeOnly,
            @Query("verified_only") int verifiedOnly,
            @Query("limit") int limit,
            @Query("offset") int offset,
            @Query("division_id") Integer divisionId,
            @Query("district_id") Integer districtId,
            @Query("upazilla_id") Integer upazillaId);

    /**
     * Get nearby laundry shops
     *
     * @param latitude User's latitude
     * @param longitude User's longitude
     * @param radius Search radius in kilometers
     * @param limit Maximum number of shops to return
     * @return API response with nearby shops
     */
    @GET("shops/nearby.php")
    Call<ApiResponse<List<LaundryShopEntity>>> getNearbyShops(
            @Query("latitude") double latitude,
            @Query("longitude") double longitude,
            @Query("radius") double radius,
            @Query("limit") int limit);

    /**
     * Get shop details
     *
     * @param shopId Shop ID
     * @param includeServices Whether to include services
     * @param includeItems Whether to include items
     * @return API response with shop details
     */
    @GET("shops/details.php")
    Call<ApiResponse<LaundryShopEntity>> getShopDetails(
            @Query("shop_id") int shopId,
            @Query("include_services") boolean includeServices,
            @Query("include_items") boolean includeItems);

    /**
     * Get delivery fee
     *
     * @return API response with delivery fee
     */
    @GET("settings/delivery_fee.php")
    Call<ApiResponse<DeliveryFeeResponse>> getDeliveryFee();

    /**
     * Get notifications for a user
     *
     * @param userId User ID
     * @return API response with notifications
     */
    @GET("notifications/user.php")
    Call<ApiResponse<List<Notification>>> getNotifications(@Query("user_id") int userId);

    /**
     * Mark notification as read
     *
     * @param notificationId Notification ID
     * @param userId User ID for authentication
     * @return API response
     */
    @FormUrlEncoded
    @POST("notifications/mark_read.php")
    Call<ApiResponse<Object>> markNotificationAsRead(
            @Field("notification_id") int notificationId,
            @Field("user_id") int userId);

    /**
     * Mark all notifications as read for a user
     *
     * @param userId User ID
     * @return API response
     */
    @FormUrlEncoded
    @POST("notifications/mark_all_read.php")
    Call<ApiResponse<Object>> markAllNotificationsAsRead(@Field("user_id") int userId);

    /**
     * Check notification read status
     *
     * @param notificationId Notification ID
     * @return API response with notification details
     */
    @GET("notifications/check_read_status.php")
    Call<ApiResponse<Object>> checkNotificationReadStatus(@Query("notification_id") int notificationId);

    /**
     * Create a notification if it doesn't exist
     *
     * @param notificationId Notification ID
     * @param userId User ID
     * @param title Notification title
     * @param message Notification message
     * @param type Notification type
     * @param isRead Whether the notification is read (0 or 1)
     * @param orderId Order ID (optional)
     * @param imageUrl Image URL (optional)
     * @return API response with notification details
     */
    @FormUrlEncoded
    @POST("notifications/create_if_not_exists.php")
    Call<Object> createNotificationIfNotExists(
            @Field("notification_id") int notificationId,
            @Field("user_id") int userId,
            @Field("title") String title,
            @Field("message") String message,
            @Field("type") String type,
            @Field("is_read") int isRead,
            @Field("order_id") Integer orderId,
            @Field("image_url") String imageUrl);

    /**
     * Register FCM token
     *
     * @param request FCM token registration request
     * @return API response
     */
    @POST("fcm/register_token.php")
    Call<Object> registerFCMToken(@retrofit2.http.Body FCMTokenRequest request);

    /**
     * Send FCM notification
     *
     * @param title Notification title
     * @param message Notification message
     * @param type Notification type
     * @param userId User ID (optional)
     * @param orderId Order ID (optional)
     * @param data Additional data (optional)
     * @return API response
     */
    @FormUrlEncoded
    @POST("fcm/send_notification.php")
    Call<ApiResponse<Object>> sendNotification(
            @Field("title") String title,
            @Field("message") String message,
            @Field("type") String type,
            @Field("user_id") Integer userId,
            @Field("order_id") Integer orderId,
            @Field("data") String data);

    /**
     * Send order status update notification
     *
     * @param orderId Order ID
     * @param status New order status
     * @param userId User ID
     * @param notes Optional notes
     * @param updatedBy ID of the person who updated the status
     * @param updatedByType Type of updater (admin, shop_owner, delivery_personnel)
     * @return API response
     */
    @FormUrlEncoded
    @POST("orders/send_status_notification.php")
    Call<ApiResponse<Object>> sendOrderStatusNotification(
            @Field("order_id") int orderId,
            @Field("status") String status,
            @Field("user_id") int userId,
            @Field("notes") String notes,
            @Field("updated_by") Integer updatedBy,
            @Field("updated_by_type") String updatedByType);
}
