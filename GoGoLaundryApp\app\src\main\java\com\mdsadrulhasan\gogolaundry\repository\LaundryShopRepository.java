package com.mdsadrulhasan.gogolaundry.repository;

import android.util.Log;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MediatorLiveData;
import androidx.lifecycle.MutableLiveData;

import com.mdsadrulhasan.gogolaundry.GoGoLaundryApp;
import com.mdsadrulhasan.gogolaundry.api.ApiClient;
import com.mdsadrulhasan.gogolaundry.api.ApiResponse;
import com.mdsadrulhasan.gogolaundry.api.ApiService;
import com.mdsadrulhasan.gogolaundry.data.Resource;
import com.mdsadrulhasan.gogolaundry.database.AppDatabase;
import com.mdsadrulhasan.gogolaundry.database.dao.LaundryShopDao;
import com.mdsadrulhasan.gogolaundry.database.dao.ShopItemDao;
import com.mdsadrulhasan.gogolaundry.database.dao.ShopServiceDao;
import com.mdsadrulhasan.gogolaundry.database.entity.LaundryShopEntity;
import com.mdsadrulhasan.gogolaundry.database.entity.ShopItemEntity;
import com.mdsadrulhasan.gogolaundry.database.entity.ShopServiceEntity;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

/**
 * Repository for handling laundry shop data
 */
public class LaundryShopRepository {

    private static final String TAG = "LaundryShopRepository";
    private static LaundryShopRepository instance;

    private final ApiService apiService;
    private final LaundryShopDao laundryShopDao;
    private final ShopServiceDao shopServiceDao;
    private final ShopItemDao shopItemDao;
    private final Executor executor;

    private LaundryShopRepository() {
        apiService = ApiClient.getApiService(GoGoLaundryApp.getInstance());
        AppDatabase database = GoGoLaundryApp.getInstance().getDatabase();
        laundryShopDao = database.laundryShopDao();
        shopServiceDao = database.shopServiceDao();
        shopItemDao = database.shopItemDao();
        executor = Executors.newFixedThreadPool(4);
    }

    /**
     * Get singleton instance
     */
    public static synchronized LaundryShopRepository getInstance() {
        if (instance == null) {
            instance = new LaundryShopRepository();
        }
        return instance;
    }

    /**
     * Get all shops with refresh option
     */
    public LiveData<Resource<List<LaundryShopEntity>>> getAllShops(boolean forceRefresh) {
        Log.d(TAG, "getAllShops() called with forceRefresh: " + forceRefresh);

        return new NetworkBoundResource<List<LaundryShopEntity>, List<LaundryShopEntity>>() {
            @Override
            protected void saveCallResult(List<LaundryShopEntity> item) {
                Log.d(TAG, "saveCallResult() - Saving " + (item != null ? item.size() : 0) + " shops to database");
                executor.execute(() -> {
                    laundryShopDao.clearAll();
                    laundryShopDao.insertAll(item);
                    Log.d(TAG, "saveCallResult() - Shops saved to database successfully");
                });
            }

            @Override
            protected boolean shouldFetch(List<LaundryShopEntity> data) {
                boolean shouldFetch = forceRefresh || data == null || data.isEmpty();
                Log.d(TAG, "shouldFetch() - Result: " + shouldFetch + " (forceRefresh: " + forceRefresh + ", data count: " + (data != null ? data.size() : "null") + ")");
                return shouldFetch;
            }

            @Override
            protected LiveData<List<LaundryShopEntity>> loadFromDb() {
                Log.d(TAG, "loadFromDb() - Loading shops from local database");
                return laundryShopDao.getAllShops();
            }

            @Override
            protected Call<ApiResponse<List<LaundryShopEntity>>> createCall() {
                Log.d(TAG, "createCall() - Creating API call to fetch all shops (active_only=1, verified_only=1, limit=50)");
                return apiService.getAllShops(1, 1, 50, 0, null, null, null);
            }
        }.asLiveData();
    }

    /**
     * Get nearby shops within radius
     */
    public LiveData<Resource<List<LaundryShopEntity>>> getNearbyShops(double latitude, double longitude, double radiusKm) {
        Log.d(TAG, "getNearbyShops() called with lat: " + latitude + ", lng: " + longitude + ", radius: " + radiusKm + "km");

        return new NetworkBoundResource<List<LaundryShopEntity>, List<LaundryShopEntity>>() {
            @Override
            protected void saveCallResult(List<LaundryShopEntity> item) {
                Log.d(TAG, "saveCallResult() for nearby shops - Saving " + (item != null ? item.size() : 0) + " shops to database");
                executor.execute(() -> {
                    try {
                        if (item != null && !item.isEmpty()) {
                            // Update existing shops with nearby results
                            for (LaundryShopEntity shop : item) {
                                laundryShopDao.insert(shop);
                            }
                            Log.d(TAG, "Nearby shops saved to database successfully");
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error saving nearby shops to database", e);
                    }
                });
            }

            @Override
            protected boolean shouldFetch(List<LaundryShopEntity> data) {
                // Always fetch from API for nearby shops to get real-time data
                boolean shouldFetch = true;
                Log.d(TAG, "shouldFetch() for nearby shops - Result: " + shouldFetch + " (always fetch for location-based queries)");
                return shouldFetch;
            }

            @Override
            protected LiveData<List<LaundryShopEntity>> loadFromDb() {
                Log.d(TAG, "loadFromDb() for nearby shops - Loading from local database as fallback");
                // Calculate bounding box for database query
                LocationRepository.BoundingBox bounds = LocationRepository.getBoundingBox(latitude, longitude, radiusKm);
                return laundryShopDao.getShopsInBounds(bounds.minLat, bounds.maxLat, bounds.minLng, bounds.maxLng);
            }

            @Override
            protected Call<ApiResponse<List<LaundryShopEntity>>> createCall() {
                Log.d(TAG, "createCall() for nearby shops - Making API call with lat: " + latitude + ", lng: " + longitude + ", radius: " + radiusKm + "km");
                return apiService.getNearbyShops(latitude, longitude, radiusKm, 50);
            }
        }.asLiveData();
    }

    /**
     * Get shop by ID
     */
    public LiveData<Resource<LaundryShopEntity>> getShopById(int shopId) {
        Log.d(TAG, "getShopById() called for shopId: " + shopId);
        MediatorLiveData<Resource<LaundryShopEntity>> result = new MediatorLiveData<>();

        LiveData<LaundryShopEntity> dbSource = laundryShopDao.getShopById(shopId);
        result.addSource(dbSource, shop -> {
            if (shop != null) {
                Log.d(TAG, "Shop found in database: " + shop.getName() + " (ID: " + shop.getId() + ")");
                result.setValue(Resource.success(shop));
            } else {
                Log.w(TAG, "Shop not found in database for ID: " + shopId);
                result.setValue(Resource.error("Shop not found", null));
            }
        });

        return result;
    }

    /**
     * Search shops by query
     */
    public LiveData<Resource<List<LaundryShopEntity>>> searchShops(String query) {
        Log.d(TAG, "searchShops() called with query: '" + query + "'");

        return new NetworkBoundResource<List<LaundryShopEntity>, List<LaundryShopEntity>>() {
            @Override
            protected void saveCallResult(List<LaundryShopEntity> item) {
                Log.d(TAG, "saveCallResult() for search - Saving " + (item != null ? item.size() : 0) + " shops to database");
                executor.execute(() -> {
                    try {
                        // Update existing shops with search results
                        if (item != null && !item.isEmpty()) {
                            Log.d(TAG, "Inserting " + item.size() + " search results into database");

                            // Log details of shops being saved
                            for (int i = 0; i < Math.min(3, item.size()); i++) {
                                LaundryShopEntity shop = item.get(i);
                                Log.d(TAG, "  Saving shop " + (i+1) + ": " + shop.getName() + " (ID: " + shop.getId() +
                                      ", Active: " + shop.isActive() + ", Lat: " + shop.getLatitude() + ", Lng: " + shop.getLongitude() + ")");
                            }

                            laundryShopDao.insertAll(item);
                            Log.d(TAG, "Successfully inserted search results into database");

                            // Verify insertion by checking database count
                            try {
                                Thread.sleep(100); // Give database time to process
                                int totalCount = laundryShopDao.getAllShopCount();
                                Log.d(TAG, "Total shops in database after insertion: " + totalCount);
                                Log.d(TAG, "Database insertion completed for search results");
                            } catch (InterruptedException e) {
                                Log.e(TAG, "Sleep interrupted", e);
                            } catch (Exception e) {
                                Log.e(TAG, "Error checking database count", e);
                            }
                        } else {
                            Log.w(TAG, "No search results to save to database");
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error saving search results to database", e);
                    }
                });
            }

            @Override
            protected boolean shouldFetch(List<LaundryShopEntity> data) {
                // Always fetch from network for search to get latest results
                boolean shouldFetch = true;
                Log.d(TAG, "shouldFetch() for search query '" + query + "': " + shouldFetch +
                          " (cached data: " + (data != null ? data.size() : 0) + " items)");
                return shouldFetch;
            }

            @Override
            protected LiveData<List<LaundryShopEntity>> loadFromDb() {
                Log.d(TAG, "loadFromDb() for search query: '" + query + "'");
                return laundryShopDao.searchShops(query);
            }

            @Override
            protected Call<ApiResponse<List<LaundryShopEntity>>> createCall() {
                Log.d(TAG, "createCall() for search - Making API call with query: '" + query + "'");
                Call<ApiResponse<List<LaundryShopEntity>>> call = apiService.searchShops(query, 1, 1, 20, 0, null, null, null);

                // Add callback to log API response
                call.enqueue(new retrofit2.Callback<ApiResponse<List<LaundryShopEntity>>>() {
                    @Override
                    public void onResponse(retrofit2.Call<ApiResponse<List<LaundryShopEntity>>> call,
                                         retrofit2.Response<ApiResponse<List<LaundryShopEntity>>> response) {
                        if (response.isSuccessful() && response.body() != null) {
                            ApiResponse<List<LaundryShopEntity>> apiResponse = response.body();
                            Log.d(TAG, "API search response - Success: " + apiResponse.isSuccess() +
                                      ", Message: " + apiResponse.getMessage());

                            if (apiResponse.isSuccess() && apiResponse.getData() != null) {
                                List<LaundryShopEntity> shops = apiResponse.getData();
                                Log.d(TAG, "API search returned " + shops.size() + " shops for query '" + query + "'");

                                if (!shops.isEmpty()) {
                                    Log.d(TAG, "First few API results:");
                                    for (int i = 0; i < Math.min(3, shops.size()); i++) {
                                        LaundryShopEntity shop = shops.get(i);
                                        Log.d(TAG, "  " + (i+1) + ". " + shop.getName() + " (ID: " + shop.getId() +
                                                  ", Rating: " + shop.getRating() + ")");
                                    }
                                }
                            } else {
                                Log.w(TAG, "API search failed or returned no data for query '" + query + "'");
                            }
                        } else {
                            Log.e(TAG, "API search request failed - Response code: " + response.code() +
                                      " for query '" + query + "'");
                        }
                    }

                    @Override
                    public void onFailure(retrofit2.Call<ApiResponse<List<LaundryShopEntity>>> call, Throwable t) {
                        Log.e(TAG, "API search request failed with exception for query '" + query + "'", t);
                    }
                });

                return call;
            }
        }.asLiveData();
    }

    /**
     * Get shops by location (division, district, upazilla)
     */
    public LiveData<Resource<List<LaundryShopEntity>>> getShopsByLocation(Integer divisionId, Integer districtId, Integer upazillaId) {
        MediatorLiveData<Resource<List<LaundryShopEntity>>> result = new MediatorLiveData<>();
        result.setValue(Resource.loading(null));

        LiveData<List<LaundryShopEntity>> dbSource = laundryShopDao.getShopsByLocation(divisionId, districtId, upazillaId);
        result.addSource(dbSource, shops -> {
            if (shops != null) {
                result.setValue(Resource.success(shops));
            } else {
                result.setValue(Resource.error("No shops found in this location", null));
            }
        });

        return result;
    }

    /**
     * Get services offered by a shop
     */
    public LiveData<Resource<List<ShopServiceEntity>>> getShopServices(int shopId) {
        Log.e(TAG, "*** LaundryShopRepository.getShopServices() called for shopId: " + shopId + " ***");

        return new NetworkBoundResource<List<ShopServiceEntity>, LaundryShopEntity>() {
            @Override
            protected void saveCallResult(LaundryShopEntity shopDetails) {
                executor.execute(() -> {
                    try {
                        Log.d(TAG, "Processing shop details API response for services");
                        if (shopDetails != null) {
                            // Save the shop details first
                            laundryShopDao.insert(shopDetails);
                            Log.d(TAG, "Shop details saved for shopId: " + shopDetails.getId());

                            // Extract and save services from the API response
                            // Note: The API response should include services data in the shop entity
                            // For now, we'll trigger a separate call to get services
                            fetchAndSaveShopServicesFromAPI(shopDetails.getId());
                        } else {
                            Log.w(TAG, "Shop details is null, cannot save services");
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error saving shop details and services", e);
                    }
                });
            }

            @Override
            protected boolean shouldFetch(List<ShopServiceEntity> data) {
                // Always fetch if no services found, or force refresh
                boolean shouldFetch = data == null || data.isEmpty();
                Log.d(TAG, "shouldFetch services for shopId " + shopId + ": " + shouldFetch + " (data count: " + (data != null ? data.size() : "null") + ")");
                return shouldFetch;
            }

            @Override
            protected LiveData<List<ShopServiceEntity>> loadFromDb() {
                return shopServiceDao.getServicesByShopId(shopId);
            }

            @Override
            protected Call<ApiResponse<LaundryShopEntity>> createCall() {
                Log.d(TAG, "Creating API call to fetch shop details with services for shopId: " + shopId);
                return apiService.getShopDetails(shopId, true, false);
            }
        }.asLiveData();
    }

    /**
     * Get items offered by a shop
     */
    public LiveData<Resource<List<ShopItemEntity>>> getShopItems(int shopId) {
        Log.d(TAG, "getShopItems() called for shopId: " + shopId);

        return new NetworkBoundResource<List<ShopItemEntity>, LaundryShopEntity>() {
            @Override
            protected void saveCallResult(LaundryShopEntity shopDetails) {
                executor.execute(() -> {
                    try {
                        Log.d(TAG, "Processing shop details API response for items");
                        if (shopDetails != null) {
                            // Save the shop details first
                            laundryShopDao.insert(shopDetails);
                            Log.d(TAG, "Shop details saved for shopId: " + shopDetails.getId());

                            // Extract and save items from the API response
                            // Note: The API response should include items data in the shop entity
                            // For now, we'll trigger a separate call to get items
                            fetchAndSaveShopItemsFromAPI(shopDetails.getId());
                        } else {
                            Log.w(TAG, "Shop details is null, cannot save items");
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error saving shop details and items", e);
                    }
                });
            }

            @Override
            protected boolean shouldFetch(List<ShopItemEntity> data) {
                // Always fetch if no items found, or force refresh
                boolean shouldFetch = data == null || data.isEmpty();
                Log.d(TAG, "shouldFetch items for shopId " + shopId + ": " + shouldFetch + " (data count: " + (data != null ? data.size() : "null") + ")");
                return shouldFetch;
            }

            @Override
            protected LiveData<List<ShopItemEntity>> loadFromDb() {
                return shopItemDao.getItemsByShopId(shopId);
            }

            @Override
            protected Call<ApiResponse<LaundryShopEntity>> createCall() {
                Log.d(TAG, "Creating API call to fetch shop details with items for shopId: " + shopId);
                return apiService.getShopDetails(shopId, false, true);
            }
        }.asLiveData();
    }

    /**
     * Get items by shop and service
     */
    public LiveData<Resource<List<ShopItemEntity>>> getShopItemsByService(int shopId, int serviceId) {
        MediatorLiveData<Resource<List<ShopItemEntity>>> result = new MediatorLiveData<>();
        result.setValue(Resource.loading(null));

        LiveData<List<ShopItemEntity>> dbSource = shopItemDao.getItemsByShopAndService(shopId, serviceId);
        result.addSource(dbSource, items -> {
            if (items != null) {
                result.setValue(Resource.success(items));
            } else {
                result.setValue(Resource.error("No items found", null));
            }
        });

        return result;
    }

    /**
     * Get top rated shops
     */
    public LiveData<Resource<List<LaundryShopEntity>>> getTopRatedShops(int limit) {
        MediatorLiveData<Resource<List<LaundryShopEntity>>> result = new MediatorLiveData<>();
        result.setValue(Resource.loading(null));

        LiveData<List<LaundryShopEntity>> dbSource = laundryShopDao.getTopRatedShops(limit);
        result.addSource(dbSource, shops -> {
            if (shops != null) {
                result.setValue(Resource.success(shops));
            } else {
                result.setValue(Resource.error("No top rated shops found", null));
            }
        });

        return result;
    }

    /**
     * Refresh shop data from server
     */
    public void refreshShops() {
        // TODO: Implement API call to refresh shop data
        Log.d(TAG, "Refreshing shop data from server");
    }

    /**
     * Helper method to fetch and save shop services from API
     */
    private void fetchAndSaveShopServicesFromAPI(int shopId) {
        Log.d(TAG, "fetchAndSaveShopServicesFromAPI() called for shopId: " + shopId);

        Call<ApiResponse<LaundryShopEntity>> call = apiService.getShopDetails(shopId, true, false);
        call.enqueue(new Callback<ApiResponse<LaundryShopEntity>>() {
            @Override
            public void onResponse(Call<ApiResponse<LaundryShopEntity>> call, Response<ApiResponse<LaundryShopEntity>> response) {
                if (response.isSuccessful() && response.body() != null && response.body().isSuccess()) {
                    LaundryShopEntity shopDetails = response.body().getData();
                    if (shopDetails != null) {
                        Log.d(TAG, "Successfully fetched shop details with services for shopId: " + shopId);
                        // The services should be included in the shop details response
                        // For now, we'll create sample services data since the API structure might need adjustment
                        createSampleServicesForShop(shopId);
                    }
                } else {
                    Log.e(TAG, "Failed to fetch shop services from API for shopId: " + shopId);
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<LaundryShopEntity>> call, Throwable t) {
                Log.e(TAG, "API call failed for shop services, shopId: " + shopId, t);
            }
        });
    }

    /**
     * Helper method to fetch and save shop items from API
     */
    private void fetchAndSaveShopItemsFromAPI(int shopId) {
        Log.d(TAG, "fetchAndSaveShopItemsFromAPI() called for shopId: " + shopId);

        Call<ApiResponse<LaundryShopEntity>> call = apiService.getShopDetails(shopId, false, true);
        call.enqueue(new Callback<ApiResponse<LaundryShopEntity>>() {
            @Override
            public void onResponse(Call<ApiResponse<LaundryShopEntity>> call, Response<ApiResponse<LaundryShopEntity>> response) {
                if (response.isSuccessful() && response.body() != null && response.body().isSuccess()) {
                    LaundryShopEntity shopDetails = response.body().getData();
                    if (shopDetails != null) {
                        Log.d(TAG, "Successfully fetched shop details with items for shopId: " + shopId);
                        // The items should be included in the shop details response
                        // For now, we'll create sample items data since the API structure might need adjustment
                        createSampleItemsForShop(shopId);
                    }
                } else {
                    Log.e(TAG, "Failed to fetch shop items from API for shopId: " + shopId);
                }
            }

            @Override
            public void onFailure(Call<ApiResponse<LaundryShopEntity>> call, Throwable t) {
                Log.e(TAG, "API call failed for shop items, shopId: " + shopId, t);
            }
        });
    }

    /**
     * Create sample services for a shop (temporary solution)
     */
    private void createSampleServicesForShop(int shopId) {
        Log.d(TAG, "Creating sample services for shopId: " + shopId);

        executor.execute(() -> {
            try {
                // Clear existing services for this shop
                shopServiceDao.clearShopServices(shopId);

                // Create sample services
                List<ShopServiceEntity> services = new ArrayList<>();

                // Service 1: Wash & Fold
                ShopServiceEntity service1 = new ShopServiceEntity();
                service1.setShopId(shopId);
                service1.setServiceId(1);
                service1.setServiceName("Wash & Fold");
                service1.setServiceBnName("ধোয়া ও ভাঁজ");
                service1.setDescription("Professional washing and folding service for your everyday clothes");
                service1.setBnDescription("আপনার দৈনন্দিন কাপড়ের জন্য পেশাদার ধোয়া এবং ভাঁজ সেবা");
                service1.setBasePrice(50.0);
                service1.setServiceImageUrl("https://example.com/wash-fold.png");
                service1.setEstimatedHours(24);
                service1.setAvailable(true);
                service1.setCreatedAt(new Date());
                services.add(service1);

                // Service 2: Dry Cleaning
                ShopServiceEntity service2 = new ShopServiceEntity();
                service2.setShopId(shopId);
                service2.setServiceId(2);
                service2.setServiceName("Dry Cleaning");
                service2.setServiceBnName("ড্রাই ক্লিনিং");
                service2.setDescription("Specialized dry cleaning for delicate fabrics and formal wear");
                service2.setBnDescription("নাজুক কাপড় এবং ফর্মাল পোশাকের জন্য বিশেষায়িত ড্রাই ক্লিনিং");
                service2.setBasePrice(120.0);
                service2.setServiceImageUrl("https://example.com/dry-clean.png");
                service2.setEstimatedHours(48);
                service2.setAvailable(true);
                service2.setCreatedAt(new Date());
                services.add(service2);

                // Service 3: Ironing
                ShopServiceEntity service3 = new ShopServiceEntity();
                service3.setShopId(shopId);
                service3.setServiceId(3);
                service3.setServiceName("Ironing");
                service3.setServiceBnName("ইস্ত্রি");
                service3.setDescription("Professional ironing service for crisp, wrinkle-free clothes");
                service3.setBnDescription("খাস্তা, ভাঁজমুক্ত কাপড়ের জন্য পেশাদার ইস্ত্রি সেবা");
                service3.setBasePrice(30.0);
                service3.setServiceImageUrl("https://example.com/ironing.png");
                service3.setEstimatedHours(12);
                service3.setAvailable(true);
                service3.setCreatedAt(new Date());
                services.add(service3);

                // Insert services
                shopServiceDao.insertAll(services);
                Log.d(TAG, "Successfully created " + services.size() + " sample services for shopId: " + shopId);

            } catch (Exception e) {
                Log.e(TAG, "Error creating sample services for shopId: " + shopId, e);
            }
        });
    }

    /**
     * Create sample items for a shop (temporary solution)
     */
    private void createSampleItemsForShop(int shopId) {
        Log.d(TAG, "Creating sample items for shopId: " + shopId);

        executor.execute(() -> {
            try {
                // Clear existing items for this shop
                shopItemDao.clearShopItems(shopId);

                // Create sample items
                List<ShopItemEntity> items = new ArrayList<>();

                // Item 1: Men's Shirt
                ShopItemEntity item1 = new ShopItemEntity();
                item1.setShopId(shopId);
                item1.setItemId(1);
                item1.setItemName("Men's Shirt");
                item1.setItemBnName("পুরুষের শার্ট");
                item1.setDescription("Cotton or formal shirts for men, professionally washed and pressed");
                item1.setBnDescription("পুরুষদের জন্য সুতি বা ফর্মাল শার্ট, পেশাদারভাবে ধোয়া এবং প্রেস করা");
                item1.setItemImageUrl("https://example.com/mens-shirt.png");
                item1.setCustomPrice(50.0);
                item1.setDefaultPrice(45.0);
                item1.setServiceName("Wash & Fold");
                item1.setEstimatedHours(24);
                item1.setAvailable(true);
                item1.setCreatedAt(new Date());
                items.add(item1);

                // Item 2: Women's Blouse
                ShopItemEntity item2 = new ShopItemEntity();
                item2.setShopId(shopId);
                item2.setItemId(2);
                item2.setItemName("Women's Blouse");
                item2.setItemBnName("মহিলাদের ব্লাউজ");
                item2.setDescription("Delicate women's blouses with careful handling and gentle cleaning");
                item2.setBnDescription("যত্নসহকারে পরিচালনা এবং মৃদু পরিষ্কারের সাথে নাজুক মহিলাদের ব্লাউজ");
                item2.setItemImageUrl("https://example.com/womens-blouse.png");
                item2.setCustomPrice(60.0);
                item2.setDefaultPrice(55.0);
                item2.setServiceName("Wash & Fold");
                item2.setEstimatedHours(24);
                item2.setAvailable(true);
                item2.setCreatedAt(new Date());
                items.add(item2);

                // Item 3: Pants
                ShopItemEntity item3 = new ShopItemEntity();
                item3.setShopId(shopId);
                item3.setItemId(3);
                item3.setItemName("Pants");
                item3.setItemBnName("প্যান্ট");
                item3.setDescription("Formal and casual pants with professional dry cleaning treatment");
                item3.setBnDescription("পেশাদার ড্রাই ক্লিনিং ট্রিটমেন্ট সহ ফর্মাল এবং ক্যাজুয়াল প্যান্ট");
                item3.setItemImageUrl("https://example.com/pants.png");
                item3.setCustomPrice(70.0);
                item3.setDefaultPrice(65.0);
                item3.setServiceName("Dry Cleaning");
                item3.setEstimatedHours(48);
                item3.setAvailable(true);
                item3.setCreatedAt(new Date());
                items.add(item3);

                // Item 4: Suit
                ShopItemEntity item4 = new ShopItemEntity();
                item4.setShopId(shopId);
                item4.setItemId(4);
                item4.setItemName("Suit");
                item4.setItemBnName("স্যুট");
                item4.setDescription("Premium suit cleaning with specialized care for formal wear");
                item4.setBnDescription("ফর্মাল পোশাকের জন্য বিশেষ যত্ন সহ প্রিমিয়াম স্যুট পরিষ্কার");
                item4.setItemImageUrl("https://example.com/suit.png");
                item4.setCustomPrice(150.0);
                item4.setDefaultPrice(140.0);
                item4.setServiceName("Dry Cleaning");
                item4.setEstimatedHours(48);
                item4.setAvailable(true);
                item4.setCreatedAt(new Date());
                items.add(item4);

                // Insert items
                shopItemDao.insertAll(items);
                Log.d(TAG, "Successfully created " + items.size() + " sample items for shopId: " + shopId);

            } catch (Exception e) {
                Log.e(TAG, "Error creating sample items for shopId: " + shopId, e);
            }
        });
    }

    /**
     * Abstract class for network bound resource pattern
     */
    private abstract class NetworkBoundResource<ResultType, RequestType> {
        private final MediatorLiveData<Resource<ResultType>> result = new MediatorLiveData<>();
        private volatile boolean networkFetchStarted = false; // Volatile flag to prevent multiple network calls
        private volatile Call<ApiResponse<RequestType>> currentCall = null; // Track current call

        public NetworkBoundResource() {
            result.setValue(Resource.loading(null));

            LiveData<ResultType> dbSource = loadFromDb();
            result.addSource(dbSource, data -> {
                result.removeSource(dbSource);
                if (shouldFetch(data) && !networkFetchStarted) {
                    fetchFromNetwork(dbSource);
                } else {
                    result.addSource(dbSource, newData -> result.setValue(Resource.success(newData)));
                }
            });
        }

        // ... inside NetworkBoundResource private class ...

        private void fetchFromNetwork(LiveData<ResultType> dbSource) {
            // Use synchronized block to prevent race conditions
            synchronized (this) {
                if (networkFetchStarted) {
                    // If network fetch already started, just return
                    return;
                }
                networkFetchStarted = true; // Set flag to prevent multiple calls
            }

            // Always create a new Call object to avoid "Already executed" error
            // Use try-catch to handle any potential issues with call creation
            try {
                currentCall = createCall();
                if (currentCall != null) {
                    result.addSource(dbSource, newData -> result.setValue(Resource.loading(newData)));

                    // Clone the call to ensure we have a fresh instance
                    Call<ApiResponse<RequestType>> freshCall = currentCall.clone();

                    freshCall.enqueue(new Callback<ApiResponse<RequestType>>() {
                        @Override
                        public void onResponse(Call<ApiResponse<RequestType>> call, Response<ApiResponse<RequestType>> response) {
                            Log.d(TAG, "NetworkBoundResource - API Response received");
                            Log.d(TAG, "NetworkBoundResource - Response code: " + response.code());
                            Log.d(TAG, "NetworkBoundResource - Response successful: " + response.isSuccessful());

                            result.removeSource(dbSource);
                            if (response.isSuccessful() && response.body() != null && response.body().isSuccess()) {
                                Log.d(TAG, "NetworkBoundResource - API call successful, saving data");
                                executor.execute(() -> {
                                    saveCallResult(response.body().getData());
                                    // Post back to main thread for LiveData operations
                                    new android.os.Handler(android.os.Looper.getMainLooper()).post(() -> {
                                        result.addSource(loadFromDb(), newData -> result.setValue(Resource.success(newData)));
                                    });
                                });
                            } else {
                                // --- FIX STARTS HERE ---
                                String baseErrorMsg = "API call failed";
                                String specificErrorDetails = "";
                                if (response.body() != null) {
                                    specificErrorDetails = " - " + response.body().getMessage();
                                } else {
                                    specificErrorDetails = " - Response code: " + response.code();
                                }
                                // Create a final variable to hold the complete error message
                                final String finalErrorMsg = baseErrorMsg + specificErrorDetails;
                                // --- FIX ENDS HERE ---

                                Log.e(TAG, "NetworkBoundResource - " + finalErrorMsg);
                                // Use the final variable in the lambda
                                result.addSource(dbSource, newData -> result.setValue(Resource.error(finalErrorMsg, newData)));
                            }
                            // Clear the current call reference
                            synchronized (NetworkBoundResource.this) {
                                currentCall = null;
                            }
                        }

                        @Override
                        public void onFailure(Call<ApiResponse<RequestType>> call, Throwable t) {
                            Log.e(TAG, "NetworkBoundResource - API call failed: " + t.getMessage(), t);
                            result.removeSource(dbSource);
                            // Use the final variable in the lambda (t.getMessage() is effectively final here)
                            final String finalFailureMsg = t.getMessage() != null ? t.getMessage() : "Unknown API failure"; // Also make this final
                            result.addSource(dbSource, newData -> result.setValue(Resource.error(finalFailureMsg, newData)));
                            // Clear the current call reference
                            synchronized (NetworkBoundResource.this) {
                                currentCall = null;
                            }
                        }
                    });
                } else {
                    // If createCall returned null, just show db data
                    result.addSource(dbSource, newData -> result.setValue(Resource.success(newData)));
                    // Reset the flag as no network call was made
                    synchronized (this) {
                        networkFetchStarted = false;
                        currentCall = null;
                    }
                }
            } catch (Exception e) {
                Log.e(TAG, "Error creating or executing API call", e);
                final String finalErrorMessage = "Failed to create API call: " + (e.getMessage() != null ? e.getMessage() : "Unknown error"); // Make this final
                result.addSource(dbSource, newData -> result.setValue(Resource.error(finalErrorMessage, newData)));
                // Reset the flag in case of error during call creation/execution setup
                synchronized (this) {
                    networkFetchStarted = false;
                    currentCall = null;
                }
            }
        }

// ... rest of the NetworkBoundResource class ...

        protected abstract void saveCallResult(RequestType item);
        protected abstract boolean shouldFetch(ResultType data);
        protected abstract LiveData<ResultType> loadFromDb();
        protected abstract Call<ApiResponse<RequestType>> createCall();

        public LiveData<Resource<ResultType>> asLiveData() {
            return result;
        }
    }
}
