package com.mdsadrulhasan.gogolaundry.database.dao;

import android.database.Cursor;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.LiveData;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.mdsadrulhasan.gogolaundry.database.converters.DateConverter;
import com.mdsadrulhasan.gogolaundry.database.entity.OrderEntity;
import com.mdsadrulhasan.gogolaundry.database.entity.OrderItemEntity;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class OrderDao_Impl implements OrderDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<OrderEntity> __insertionAdapterOfOrderEntity;

  private final EntityInsertionAdapter<OrderItemEntity> __insertionAdapterOfOrderItemEntity;

  private final EntityDeletionOrUpdateAdapter<OrderEntity> __updateAdapterOfOrderEntity;

  private final EntityDeletionOrUpdateAdapter<OrderItemEntity> __updateAdapterOfOrderItemEntity;

  private final SharedSQLiteStatement __preparedStmtOfDeleteOrder;

  private final SharedSQLiteStatement __preparedStmtOfDeleteOrderItem;

  private final SharedSQLiteStatement __preparedStmtOfDeleteOrderItemsByOrderId;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAllOrders;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAllOrderItems;

  public OrderDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfOrderEntity = new EntityInsertionAdapter<OrderEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `orders` (`id`,`order_number`,`tracking_number`,`user_id`,`delivery_personnel_id`,`promo_code_id`,`subtotal`,`discount`,`delivery_fee`,`total`,`payment_method`,`payment_status`,`status`,`pickup_address`,`pickup_division_id`,`pickup_district_id`,`pickup_upazilla_id`,`pickup_date`,`pickup_time_slot`,`delivery_address`,`delivery_division_id`,`delivery_district_id`,`delivery_upazilla_id`,`delivery_date`,`delivery_time_slot`,`notes`,`created_at`,`updated_at`,`customerName`,`customerPhone`,`deliveryPersonName`,`deliveryPersonPhone`,`promoCode`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          final OrderEntity entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getOrderNumber() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getOrderNumber());
        }
        if (entity.getTrackingNumber() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getTrackingNumber());
        }
        statement.bindLong(4, entity.getUserId());
        if (entity.getDeliveryPersonnelId() == null) {
          statement.bindNull(5);
        } else {
          statement.bindLong(5, entity.getDeliveryPersonnelId());
        }
        if (entity.getPromoCodeId() == null) {
          statement.bindNull(6);
        } else {
          statement.bindLong(6, entity.getPromoCodeId());
        }
        statement.bindDouble(7, entity.getSubtotal());
        statement.bindDouble(8, entity.getDiscount());
        statement.bindDouble(9, entity.getDeliveryFee());
        statement.bindDouble(10, entity.getTotal());
        if (entity.getPaymentMethod() == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, entity.getPaymentMethod());
        }
        if (entity.getPaymentStatus() == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, entity.getPaymentStatus());
        }
        if (entity.getStatus() == null) {
          statement.bindNull(13);
        } else {
          statement.bindString(13, entity.getStatus());
        }
        if (entity.getPickupAddress() == null) {
          statement.bindNull(14);
        } else {
          statement.bindString(14, entity.getPickupAddress());
        }
        if (entity.getPickupDivisionId() == null) {
          statement.bindNull(15);
        } else {
          statement.bindLong(15, entity.getPickupDivisionId());
        }
        if (entity.getPickupDistrictId() == null) {
          statement.bindNull(16);
        } else {
          statement.bindLong(16, entity.getPickupDistrictId());
        }
        if (entity.getPickupUpazillaId() == null) {
          statement.bindNull(17);
        } else {
          statement.bindLong(17, entity.getPickupUpazillaId());
        }
        final Long _tmp = DateConverter.dateToTimestamp(entity.getPickupDate());
        if (_tmp == null) {
          statement.bindNull(18);
        } else {
          statement.bindLong(18, _tmp);
        }
        if (entity.getPickupTimeSlot() == null) {
          statement.bindNull(19);
        } else {
          statement.bindString(19, entity.getPickupTimeSlot());
        }
        if (entity.getDeliveryAddress() == null) {
          statement.bindNull(20);
        } else {
          statement.bindString(20, entity.getDeliveryAddress());
        }
        if (entity.getDeliveryDivisionId() == null) {
          statement.bindNull(21);
        } else {
          statement.bindLong(21, entity.getDeliveryDivisionId());
        }
        if (entity.getDeliveryDistrictId() == null) {
          statement.bindNull(22);
        } else {
          statement.bindLong(22, entity.getDeliveryDistrictId());
        }
        if (entity.getDeliveryUpazillaId() == null) {
          statement.bindNull(23);
        } else {
          statement.bindLong(23, entity.getDeliveryUpazillaId());
        }
        final Long _tmp_1 = DateConverter.dateToTimestamp(entity.getDeliveryDate());
        if (_tmp_1 == null) {
          statement.bindNull(24);
        } else {
          statement.bindLong(24, _tmp_1);
        }
        if (entity.getDeliveryTimeSlot() == null) {
          statement.bindNull(25);
        } else {
          statement.bindString(25, entity.getDeliveryTimeSlot());
        }
        if (entity.getNotes() == null) {
          statement.bindNull(26);
        } else {
          statement.bindString(26, entity.getNotes());
        }
        final Long _tmp_2 = DateConverter.dateToTimestamp(entity.getCreatedAt());
        if (_tmp_2 == null) {
          statement.bindNull(27);
        } else {
          statement.bindLong(27, _tmp_2);
        }
        final Long _tmp_3 = DateConverter.dateToTimestamp(entity.getUpdatedAt());
        if (_tmp_3 == null) {
          statement.bindNull(28);
        } else {
          statement.bindLong(28, _tmp_3);
        }
        if (entity.getCustomerName() == null) {
          statement.bindNull(29);
        } else {
          statement.bindString(29, entity.getCustomerName());
        }
        if (entity.getCustomerPhone() == null) {
          statement.bindNull(30);
        } else {
          statement.bindString(30, entity.getCustomerPhone());
        }
        if (entity.getDeliveryPersonName() == null) {
          statement.bindNull(31);
        } else {
          statement.bindString(31, entity.getDeliveryPersonName());
        }
        if (entity.getDeliveryPersonPhone() == null) {
          statement.bindNull(32);
        } else {
          statement.bindString(32, entity.getDeliveryPersonPhone());
        }
        if (entity.getPromoCode() == null) {
          statement.bindNull(33);
        } else {
          statement.bindString(33, entity.getPromoCode());
        }
      }
    };
    this.__insertionAdapterOfOrderItemEntity = new EntityInsertionAdapter<OrderItemEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `order_items` (`id`,`order_id`,`item_id`,`quantity`,`price`,`subtotal`,`created_at`,`updated_at`,`itemName`,`itemBnName`,`itemImageUrl`,`serviceName`,`serviceBnName`) VALUES (nullif(?, 0),?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          final OrderItemEntity entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getOrderId());
        statement.bindLong(3, entity.getItemId());
        statement.bindLong(4, entity.getQuantity());
        statement.bindDouble(5, entity.getPrice());
        statement.bindDouble(6, entity.getSubtotal());
        final Long _tmp = DateConverter.dateToTimestamp(entity.getCreatedAt());
        if (_tmp == null) {
          statement.bindNull(7);
        } else {
          statement.bindLong(7, _tmp);
        }
        final Long _tmp_1 = DateConverter.dateToTimestamp(entity.getUpdatedAt());
        if (_tmp_1 == null) {
          statement.bindNull(8);
        } else {
          statement.bindLong(8, _tmp_1);
        }
        if (entity.getItemName() == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.getItemName());
        }
        if (entity.getItemBnName() == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, entity.getItemBnName());
        }
        if (entity.getItemImageUrl() == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, entity.getItemImageUrl());
        }
        if (entity.getServiceName() == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, entity.getServiceName());
        }
        if (entity.getServiceBnName() == null) {
          statement.bindNull(13);
        } else {
          statement.bindString(13, entity.getServiceBnName());
        }
      }
    };
    this.__updateAdapterOfOrderEntity = new EntityDeletionOrUpdateAdapter<OrderEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `orders` SET `id` = ?,`order_number` = ?,`tracking_number` = ?,`user_id` = ?,`delivery_personnel_id` = ?,`promo_code_id` = ?,`subtotal` = ?,`discount` = ?,`delivery_fee` = ?,`total` = ?,`payment_method` = ?,`payment_status` = ?,`status` = ?,`pickup_address` = ?,`pickup_division_id` = ?,`pickup_district_id` = ?,`pickup_upazilla_id` = ?,`pickup_date` = ?,`pickup_time_slot` = ?,`delivery_address` = ?,`delivery_division_id` = ?,`delivery_district_id` = ?,`delivery_upazilla_id` = ?,`delivery_date` = ?,`delivery_time_slot` = ?,`notes` = ?,`created_at` = ?,`updated_at` = ?,`customerName` = ?,`customerPhone` = ?,`deliveryPersonName` = ?,`deliveryPersonPhone` = ?,`promoCode` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          final OrderEntity entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getOrderNumber() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getOrderNumber());
        }
        if (entity.getTrackingNumber() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getTrackingNumber());
        }
        statement.bindLong(4, entity.getUserId());
        if (entity.getDeliveryPersonnelId() == null) {
          statement.bindNull(5);
        } else {
          statement.bindLong(5, entity.getDeliveryPersonnelId());
        }
        if (entity.getPromoCodeId() == null) {
          statement.bindNull(6);
        } else {
          statement.bindLong(6, entity.getPromoCodeId());
        }
        statement.bindDouble(7, entity.getSubtotal());
        statement.bindDouble(8, entity.getDiscount());
        statement.bindDouble(9, entity.getDeliveryFee());
        statement.bindDouble(10, entity.getTotal());
        if (entity.getPaymentMethod() == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, entity.getPaymentMethod());
        }
        if (entity.getPaymentStatus() == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, entity.getPaymentStatus());
        }
        if (entity.getStatus() == null) {
          statement.bindNull(13);
        } else {
          statement.bindString(13, entity.getStatus());
        }
        if (entity.getPickupAddress() == null) {
          statement.bindNull(14);
        } else {
          statement.bindString(14, entity.getPickupAddress());
        }
        if (entity.getPickupDivisionId() == null) {
          statement.bindNull(15);
        } else {
          statement.bindLong(15, entity.getPickupDivisionId());
        }
        if (entity.getPickupDistrictId() == null) {
          statement.bindNull(16);
        } else {
          statement.bindLong(16, entity.getPickupDistrictId());
        }
        if (entity.getPickupUpazillaId() == null) {
          statement.bindNull(17);
        } else {
          statement.bindLong(17, entity.getPickupUpazillaId());
        }
        final Long _tmp = DateConverter.dateToTimestamp(entity.getPickupDate());
        if (_tmp == null) {
          statement.bindNull(18);
        } else {
          statement.bindLong(18, _tmp);
        }
        if (entity.getPickupTimeSlot() == null) {
          statement.bindNull(19);
        } else {
          statement.bindString(19, entity.getPickupTimeSlot());
        }
        if (entity.getDeliveryAddress() == null) {
          statement.bindNull(20);
        } else {
          statement.bindString(20, entity.getDeliveryAddress());
        }
        if (entity.getDeliveryDivisionId() == null) {
          statement.bindNull(21);
        } else {
          statement.bindLong(21, entity.getDeliveryDivisionId());
        }
        if (entity.getDeliveryDistrictId() == null) {
          statement.bindNull(22);
        } else {
          statement.bindLong(22, entity.getDeliveryDistrictId());
        }
        if (entity.getDeliveryUpazillaId() == null) {
          statement.bindNull(23);
        } else {
          statement.bindLong(23, entity.getDeliveryUpazillaId());
        }
        final Long _tmp_1 = DateConverter.dateToTimestamp(entity.getDeliveryDate());
        if (_tmp_1 == null) {
          statement.bindNull(24);
        } else {
          statement.bindLong(24, _tmp_1);
        }
        if (entity.getDeliveryTimeSlot() == null) {
          statement.bindNull(25);
        } else {
          statement.bindString(25, entity.getDeliveryTimeSlot());
        }
        if (entity.getNotes() == null) {
          statement.bindNull(26);
        } else {
          statement.bindString(26, entity.getNotes());
        }
        final Long _tmp_2 = DateConverter.dateToTimestamp(entity.getCreatedAt());
        if (_tmp_2 == null) {
          statement.bindNull(27);
        } else {
          statement.bindLong(27, _tmp_2);
        }
        final Long _tmp_3 = DateConverter.dateToTimestamp(entity.getUpdatedAt());
        if (_tmp_3 == null) {
          statement.bindNull(28);
        } else {
          statement.bindLong(28, _tmp_3);
        }
        if (entity.getCustomerName() == null) {
          statement.bindNull(29);
        } else {
          statement.bindString(29, entity.getCustomerName());
        }
        if (entity.getCustomerPhone() == null) {
          statement.bindNull(30);
        } else {
          statement.bindString(30, entity.getCustomerPhone());
        }
        if (entity.getDeliveryPersonName() == null) {
          statement.bindNull(31);
        } else {
          statement.bindString(31, entity.getDeliveryPersonName());
        }
        if (entity.getDeliveryPersonPhone() == null) {
          statement.bindNull(32);
        } else {
          statement.bindString(32, entity.getDeliveryPersonPhone());
        }
        if (entity.getPromoCode() == null) {
          statement.bindNull(33);
        } else {
          statement.bindString(33, entity.getPromoCode());
        }
        statement.bindLong(34, entity.getId());
      }
    };
    this.__updateAdapterOfOrderItemEntity = new EntityDeletionOrUpdateAdapter<OrderItemEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `order_items` SET `id` = ?,`order_id` = ?,`item_id` = ?,`quantity` = ?,`price` = ?,`subtotal` = ?,`created_at` = ?,`updated_at` = ?,`itemName` = ?,`itemBnName` = ?,`itemImageUrl` = ?,`serviceName` = ?,`serviceBnName` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          final OrderItemEntity entity) {
        statement.bindLong(1, entity.getId());
        statement.bindLong(2, entity.getOrderId());
        statement.bindLong(3, entity.getItemId());
        statement.bindLong(4, entity.getQuantity());
        statement.bindDouble(5, entity.getPrice());
        statement.bindDouble(6, entity.getSubtotal());
        final Long _tmp = DateConverter.dateToTimestamp(entity.getCreatedAt());
        if (_tmp == null) {
          statement.bindNull(7);
        } else {
          statement.bindLong(7, _tmp);
        }
        final Long _tmp_1 = DateConverter.dateToTimestamp(entity.getUpdatedAt());
        if (_tmp_1 == null) {
          statement.bindNull(8);
        } else {
          statement.bindLong(8, _tmp_1);
        }
        if (entity.getItemName() == null) {
          statement.bindNull(9);
        } else {
          statement.bindString(9, entity.getItemName());
        }
        if (entity.getItemBnName() == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, entity.getItemBnName());
        }
        if (entity.getItemImageUrl() == null) {
          statement.bindNull(11);
        } else {
          statement.bindString(11, entity.getItemImageUrl());
        }
        if (entity.getServiceName() == null) {
          statement.bindNull(12);
        } else {
          statement.bindString(12, entity.getServiceName());
        }
        if (entity.getServiceBnName() == null) {
          statement.bindNull(13);
        } else {
          statement.bindString(13, entity.getServiceBnName());
        }
        statement.bindLong(14, entity.getId());
      }
    };
    this.__preparedStmtOfDeleteOrder = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM orders WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteOrderItem = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM order_items WHERE id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteOrderItemsByOrderId = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM order_items WHERE order_id = ?";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteAllOrders = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM orders";
        return _query;
      }
    };
    this.__preparedStmtOfDeleteAllOrderItems = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM order_items";
        return _query;
      }
    };
  }

  @Override
  public long insert(final OrderEntity order) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      final long _result = __insertionAdapterOfOrderEntity.insertAndReturnId(order);
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public long insertOrderItem(final OrderItemEntity orderItem) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      final long _result = __insertionAdapterOfOrderItemEntity.insertAndReturnId(orderItem);
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public List<Long> insertOrderItems(final List<OrderItemEntity> orderItems) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      final List<Long> _result = __insertionAdapterOfOrderItemEntity.insertAndReturnIdsList(orderItems);
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public void update(final OrderEntity order) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      __updateAdapterOfOrderEntity.handle(order);
      __db.setTransactionSuccessful();
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public void updateOrderItem(final OrderItemEntity orderItem) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      __updateAdapterOfOrderItemEntity.handle(orderItem);
      __db.setTransactionSuccessful();
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public void deleteAll() {
    __db.beginTransaction();
    try {
      OrderDao.super.deleteAll();
      __db.setTransactionSuccessful();
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public void deleteOrder(final int id) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteOrder.acquire();
    int _argIndex = 1;
    _stmt.bindLong(_argIndex, id);
    try {
      __db.beginTransaction();
      try {
        _stmt.executeUpdateDelete();
        __db.setTransactionSuccessful();
      } finally {
        __db.endTransaction();
      }
    } finally {
      __preparedStmtOfDeleteOrder.release(_stmt);
    }
  }

  @Override
  public void deleteOrderItem(final int id) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteOrderItem.acquire();
    int _argIndex = 1;
    _stmt.bindLong(_argIndex, id);
    try {
      __db.beginTransaction();
      try {
        _stmt.executeUpdateDelete();
        __db.setTransactionSuccessful();
      } finally {
        __db.endTransaction();
      }
    } finally {
      __preparedStmtOfDeleteOrderItem.release(_stmt);
    }
  }

  @Override
  public void deleteOrderItemsByOrderId(final int orderId) {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteOrderItemsByOrderId.acquire();
    int _argIndex = 1;
    _stmt.bindLong(_argIndex, orderId);
    try {
      __db.beginTransaction();
      try {
        _stmt.executeUpdateDelete();
        __db.setTransactionSuccessful();
      } finally {
        __db.endTransaction();
      }
    } finally {
      __preparedStmtOfDeleteOrderItemsByOrderId.release(_stmt);
    }
  }

  @Override
  public void deleteAllOrders() {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAllOrders.acquire();
    try {
      __db.beginTransaction();
      try {
        _stmt.executeUpdateDelete();
        __db.setTransactionSuccessful();
      } finally {
        __db.endTransaction();
      }
    } finally {
      __preparedStmtOfDeleteAllOrders.release(_stmt);
    }
  }

  @Override
  public void deleteAllOrderItems() {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAllOrderItems.acquire();
    try {
      __db.beginTransaction();
      try {
        _stmt.executeUpdateDelete();
        __db.setTransactionSuccessful();
      } finally {
        __db.endTransaction();
      }
    } finally {
      __preparedStmtOfDeleteAllOrderItems.release(_stmt);
    }
  }

  @Override
  public OrderEntity getOrderById(final int id) {
    final String _sql = "SELECT * FROM orders WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, id);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfOrderNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "order_number");
      final int _cursorIndexOfTrackingNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "tracking_number");
      final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "user_id");
      final int _cursorIndexOfDeliveryPersonnelId = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_personnel_id");
      final int _cursorIndexOfPromoCodeId = CursorUtil.getColumnIndexOrThrow(_cursor, "promo_code_id");
      final int _cursorIndexOfSubtotal = CursorUtil.getColumnIndexOrThrow(_cursor, "subtotal");
      final int _cursorIndexOfDiscount = CursorUtil.getColumnIndexOrThrow(_cursor, "discount");
      final int _cursorIndexOfDeliveryFee = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_fee");
      final int _cursorIndexOfTotal = CursorUtil.getColumnIndexOrThrow(_cursor, "total");
      final int _cursorIndexOfPaymentMethod = CursorUtil.getColumnIndexOrThrow(_cursor, "payment_method");
      final int _cursorIndexOfPaymentStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "payment_status");
      final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
      final int _cursorIndexOfPickupAddress = CursorUtil.getColumnIndexOrThrow(_cursor, "pickup_address");
      final int _cursorIndexOfPickupDivisionId = CursorUtil.getColumnIndexOrThrow(_cursor, "pickup_division_id");
      final int _cursorIndexOfPickupDistrictId = CursorUtil.getColumnIndexOrThrow(_cursor, "pickup_district_id");
      final int _cursorIndexOfPickupUpazillaId = CursorUtil.getColumnIndexOrThrow(_cursor, "pickup_upazilla_id");
      final int _cursorIndexOfPickupDate = CursorUtil.getColumnIndexOrThrow(_cursor, "pickup_date");
      final int _cursorIndexOfPickupTimeSlot = CursorUtil.getColumnIndexOrThrow(_cursor, "pickup_time_slot");
      final int _cursorIndexOfDeliveryAddress = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_address");
      final int _cursorIndexOfDeliveryDivisionId = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_division_id");
      final int _cursorIndexOfDeliveryDistrictId = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_district_id");
      final int _cursorIndexOfDeliveryUpazillaId = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_upazilla_id");
      final int _cursorIndexOfDeliveryDate = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_date");
      final int _cursorIndexOfDeliveryTimeSlot = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_time_slot");
      final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
      final int _cursorIndexOfCustomerName = CursorUtil.getColumnIndexOrThrow(_cursor, "customerName");
      final int _cursorIndexOfCustomerPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "customerPhone");
      final int _cursorIndexOfDeliveryPersonName = CursorUtil.getColumnIndexOrThrow(_cursor, "deliveryPersonName");
      final int _cursorIndexOfDeliveryPersonPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "deliveryPersonPhone");
      final int _cursorIndexOfPromoCode = CursorUtil.getColumnIndexOrThrow(_cursor, "promoCode");
      final OrderEntity _result;
      if (_cursor.moveToFirst()) {
        _result = new OrderEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _result.setId(_tmpId);
        final String _tmpOrderNumber;
        if (_cursor.isNull(_cursorIndexOfOrderNumber)) {
          _tmpOrderNumber = null;
        } else {
          _tmpOrderNumber = _cursor.getString(_cursorIndexOfOrderNumber);
        }
        _result.setOrderNumber(_tmpOrderNumber);
        final String _tmpTrackingNumber;
        if (_cursor.isNull(_cursorIndexOfTrackingNumber)) {
          _tmpTrackingNumber = null;
        } else {
          _tmpTrackingNumber = _cursor.getString(_cursorIndexOfTrackingNumber);
        }
        _result.setTrackingNumber(_tmpTrackingNumber);
        final int _tmpUserId;
        _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
        _result.setUserId(_tmpUserId);
        final Integer _tmpDeliveryPersonnelId;
        if (_cursor.isNull(_cursorIndexOfDeliveryPersonnelId)) {
          _tmpDeliveryPersonnelId = null;
        } else {
          _tmpDeliveryPersonnelId = _cursor.getInt(_cursorIndexOfDeliveryPersonnelId);
        }
        _result.setDeliveryPersonnelId(_tmpDeliveryPersonnelId);
        final Integer _tmpPromoCodeId;
        if (_cursor.isNull(_cursorIndexOfPromoCodeId)) {
          _tmpPromoCodeId = null;
        } else {
          _tmpPromoCodeId = _cursor.getInt(_cursorIndexOfPromoCodeId);
        }
        _result.setPromoCodeId(_tmpPromoCodeId);
        final double _tmpSubtotal;
        _tmpSubtotal = _cursor.getDouble(_cursorIndexOfSubtotal);
        _result.setSubtotal(_tmpSubtotal);
        final double _tmpDiscount;
        _tmpDiscount = _cursor.getDouble(_cursorIndexOfDiscount);
        _result.setDiscount(_tmpDiscount);
        final double _tmpDeliveryFee;
        _tmpDeliveryFee = _cursor.getDouble(_cursorIndexOfDeliveryFee);
        _result.setDeliveryFee(_tmpDeliveryFee);
        final double _tmpTotal;
        _tmpTotal = _cursor.getDouble(_cursorIndexOfTotal);
        _result.setTotal(_tmpTotal);
        final String _tmpPaymentMethod;
        if (_cursor.isNull(_cursorIndexOfPaymentMethod)) {
          _tmpPaymentMethod = null;
        } else {
          _tmpPaymentMethod = _cursor.getString(_cursorIndexOfPaymentMethod);
        }
        _result.setPaymentMethod(_tmpPaymentMethod);
        final String _tmpPaymentStatus;
        if (_cursor.isNull(_cursorIndexOfPaymentStatus)) {
          _tmpPaymentStatus = null;
        } else {
          _tmpPaymentStatus = _cursor.getString(_cursorIndexOfPaymentStatus);
        }
        _result.setPaymentStatus(_tmpPaymentStatus);
        final String _tmpStatus;
        if (_cursor.isNull(_cursorIndexOfStatus)) {
          _tmpStatus = null;
        } else {
          _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
        }
        _result.setStatus(_tmpStatus);
        final String _tmpPickupAddress;
        if (_cursor.isNull(_cursorIndexOfPickupAddress)) {
          _tmpPickupAddress = null;
        } else {
          _tmpPickupAddress = _cursor.getString(_cursorIndexOfPickupAddress);
        }
        _result.setPickupAddress(_tmpPickupAddress);
        final Integer _tmpPickupDivisionId;
        if (_cursor.isNull(_cursorIndexOfPickupDivisionId)) {
          _tmpPickupDivisionId = null;
        } else {
          _tmpPickupDivisionId = _cursor.getInt(_cursorIndexOfPickupDivisionId);
        }
        _result.setPickupDivisionId(_tmpPickupDivisionId);
        final Integer _tmpPickupDistrictId;
        if (_cursor.isNull(_cursorIndexOfPickupDistrictId)) {
          _tmpPickupDistrictId = null;
        } else {
          _tmpPickupDistrictId = _cursor.getInt(_cursorIndexOfPickupDistrictId);
        }
        _result.setPickupDistrictId(_tmpPickupDistrictId);
        final Integer _tmpPickupUpazillaId;
        if (_cursor.isNull(_cursorIndexOfPickupUpazillaId)) {
          _tmpPickupUpazillaId = null;
        } else {
          _tmpPickupUpazillaId = _cursor.getInt(_cursorIndexOfPickupUpazillaId);
        }
        _result.setPickupUpazillaId(_tmpPickupUpazillaId);
        final Date _tmpPickupDate;
        final Long _tmp;
        if (_cursor.isNull(_cursorIndexOfPickupDate)) {
          _tmp = null;
        } else {
          _tmp = _cursor.getLong(_cursorIndexOfPickupDate);
        }
        _tmpPickupDate = DateConverter.fromTimestamp(_tmp);
        _result.setPickupDate(_tmpPickupDate);
        final String _tmpPickupTimeSlot;
        if (_cursor.isNull(_cursorIndexOfPickupTimeSlot)) {
          _tmpPickupTimeSlot = null;
        } else {
          _tmpPickupTimeSlot = _cursor.getString(_cursorIndexOfPickupTimeSlot);
        }
        _result.setPickupTimeSlot(_tmpPickupTimeSlot);
        final String _tmpDeliveryAddress;
        if (_cursor.isNull(_cursorIndexOfDeliveryAddress)) {
          _tmpDeliveryAddress = null;
        } else {
          _tmpDeliveryAddress = _cursor.getString(_cursorIndexOfDeliveryAddress);
        }
        _result.setDeliveryAddress(_tmpDeliveryAddress);
        final Integer _tmpDeliveryDivisionId;
        if (_cursor.isNull(_cursorIndexOfDeliveryDivisionId)) {
          _tmpDeliveryDivisionId = null;
        } else {
          _tmpDeliveryDivisionId = _cursor.getInt(_cursorIndexOfDeliveryDivisionId);
        }
        _result.setDeliveryDivisionId(_tmpDeliveryDivisionId);
        final Integer _tmpDeliveryDistrictId;
        if (_cursor.isNull(_cursorIndexOfDeliveryDistrictId)) {
          _tmpDeliveryDistrictId = null;
        } else {
          _tmpDeliveryDistrictId = _cursor.getInt(_cursorIndexOfDeliveryDistrictId);
        }
        _result.setDeliveryDistrictId(_tmpDeliveryDistrictId);
        final Integer _tmpDeliveryUpazillaId;
        if (_cursor.isNull(_cursorIndexOfDeliveryUpazillaId)) {
          _tmpDeliveryUpazillaId = null;
        } else {
          _tmpDeliveryUpazillaId = _cursor.getInt(_cursorIndexOfDeliveryUpazillaId);
        }
        _result.setDeliveryUpazillaId(_tmpDeliveryUpazillaId);
        final Date _tmpDeliveryDate;
        final Long _tmp_1;
        if (_cursor.isNull(_cursorIndexOfDeliveryDate)) {
          _tmp_1 = null;
        } else {
          _tmp_1 = _cursor.getLong(_cursorIndexOfDeliveryDate);
        }
        _tmpDeliveryDate = DateConverter.fromTimestamp(_tmp_1);
        _result.setDeliveryDate(_tmpDeliveryDate);
        final String _tmpDeliveryTimeSlot;
        if (_cursor.isNull(_cursorIndexOfDeliveryTimeSlot)) {
          _tmpDeliveryTimeSlot = null;
        } else {
          _tmpDeliveryTimeSlot = _cursor.getString(_cursorIndexOfDeliveryTimeSlot);
        }
        _result.setDeliveryTimeSlot(_tmpDeliveryTimeSlot);
        final String _tmpNotes;
        if (_cursor.isNull(_cursorIndexOfNotes)) {
          _tmpNotes = null;
        } else {
          _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
        }
        _result.setNotes(_tmpNotes);
        final Date _tmpCreatedAt;
        final Long _tmp_2;
        if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
          _tmp_2 = null;
        } else {
          _tmp_2 = _cursor.getLong(_cursorIndexOfCreatedAt);
        }
        _tmpCreatedAt = DateConverter.fromTimestamp(_tmp_2);
        _result.setCreatedAt(_tmpCreatedAt);
        final Date _tmpUpdatedAt;
        final Long _tmp_3;
        if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
          _tmp_3 = null;
        } else {
          _tmp_3 = _cursor.getLong(_cursorIndexOfUpdatedAt);
        }
        _tmpUpdatedAt = DateConverter.fromTimestamp(_tmp_3);
        _result.setUpdatedAt(_tmpUpdatedAt);
        final String _tmpCustomerName;
        if (_cursor.isNull(_cursorIndexOfCustomerName)) {
          _tmpCustomerName = null;
        } else {
          _tmpCustomerName = _cursor.getString(_cursorIndexOfCustomerName);
        }
        _result.setCustomerName(_tmpCustomerName);
        final String _tmpCustomerPhone;
        if (_cursor.isNull(_cursorIndexOfCustomerPhone)) {
          _tmpCustomerPhone = null;
        } else {
          _tmpCustomerPhone = _cursor.getString(_cursorIndexOfCustomerPhone);
        }
        _result.setCustomerPhone(_tmpCustomerPhone);
        final String _tmpDeliveryPersonName;
        if (_cursor.isNull(_cursorIndexOfDeliveryPersonName)) {
          _tmpDeliveryPersonName = null;
        } else {
          _tmpDeliveryPersonName = _cursor.getString(_cursorIndexOfDeliveryPersonName);
        }
        _result.setDeliveryPersonName(_tmpDeliveryPersonName);
        final String _tmpDeliveryPersonPhone;
        if (_cursor.isNull(_cursorIndexOfDeliveryPersonPhone)) {
          _tmpDeliveryPersonPhone = null;
        } else {
          _tmpDeliveryPersonPhone = _cursor.getString(_cursorIndexOfDeliveryPersonPhone);
        }
        _result.setDeliveryPersonPhone(_tmpDeliveryPersonPhone);
        final String _tmpPromoCode;
        if (_cursor.isNull(_cursorIndexOfPromoCode)) {
          _tmpPromoCode = null;
        } else {
          _tmpPromoCode = _cursor.getString(_cursorIndexOfPromoCode);
        }
        _result.setPromoCode(_tmpPromoCode);
      } else {
        _result = null;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public LiveData<OrderEntity> getOrderByIdLive(final int id) {
    final String _sql = "SELECT * FROM orders WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, id);
    return __db.getInvalidationTracker().createLiveData(new String[] {"orders"}, false, new Callable<OrderEntity>() {
      @Override
      @Nullable
      public OrderEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfOrderNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "order_number");
          final int _cursorIndexOfTrackingNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "tracking_number");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "user_id");
          final int _cursorIndexOfDeliveryPersonnelId = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_personnel_id");
          final int _cursorIndexOfPromoCodeId = CursorUtil.getColumnIndexOrThrow(_cursor, "promo_code_id");
          final int _cursorIndexOfSubtotal = CursorUtil.getColumnIndexOrThrow(_cursor, "subtotal");
          final int _cursorIndexOfDiscount = CursorUtil.getColumnIndexOrThrow(_cursor, "discount");
          final int _cursorIndexOfDeliveryFee = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_fee");
          final int _cursorIndexOfTotal = CursorUtil.getColumnIndexOrThrow(_cursor, "total");
          final int _cursorIndexOfPaymentMethod = CursorUtil.getColumnIndexOrThrow(_cursor, "payment_method");
          final int _cursorIndexOfPaymentStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "payment_status");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfPickupAddress = CursorUtil.getColumnIndexOrThrow(_cursor, "pickup_address");
          final int _cursorIndexOfPickupDivisionId = CursorUtil.getColumnIndexOrThrow(_cursor, "pickup_division_id");
          final int _cursorIndexOfPickupDistrictId = CursorUtil.getColumnIndexOrThrow(_cursor, "pickup_district_id");
          final int _cursorIndexOfPickupUpazillaId = CursorUtil.getColumnIndexOrThrow(_cursor, "pickup_upazilla_id");
          final int _cursorIndexOfPickupDate = CursorUtil.getColumnIndexOrThrow(_cursor, "pickup_date");
          final int _cursorIndexOfPickupTimeSlot = CursorUtil.getColumnIndexOrThrow(_cursor, "pickup_time_slot");
          final int _cursorIndexOfDeliveryAddress = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_address");
          final int _cursorIndexOfDeliveryDivisionId = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_division_id");
          final int _cursorIndexOfDeliveryDistrictId = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_district_id");
          final int _cursorIndexOfDeliveryUpazillaId = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_upazilla_id");
          final int _cursorIndexOfDeliveryDate = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_date");
          final int _cursorIndexOfDeliveryTimeSlot = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_time_slot");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final int _cursorIndexOfCustomerName = CursorUtil.getColumnIndexOrThrow(_cursor, "customerName");
          final int _cursorIndexOfCustomerPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "customerPhone");
          final int _cursorIndexOfDeliveryPersonName = CursorUtil.getColumnIndexOrThrow(_cursor, "deliveryPersonName");
          final int _cursorIndexOfDeliveryPersonPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "deliveryPersonPhone");
          final int _cursorIndexOfPromoCode = CursorUtil.getColumnIndexOrThrow(_cursor, "promoCode");
          final OrderEntity _result;
          if (_cursor.moveToFirst()) {
            _result = new OrderEntity();
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            _result.setId(_tmpId);
            final String _tmpOrderNumber;
            if (_cursor.isNull(_cursorIndexOfOrderNumber)) {
              _tmpOrderNumber = null;
            } else {
              _tmpOrderNumber = _cursor.getString(_cursorIndexOfOrderNumber);
            }
            _result.setOrderNumber(_tmpOrderNumber);
            final String _tmpTrackingNumber;
            if (_cursor.isNull(_cursorIndexOfTrackingNumber)) {
              _tmpTrackingNumber = null;
            } else {
              _tmpTrackingNumber = _cursor.getString(_cursorIndexOfTrackingNumber);
            }
            _result.setTrackingNumber(_tmpTrackingNumber);
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            _result.setUserId(_tmpUserId);
            final Integer _tmpDeliveryPersonnelId;
            if (_cursor.isNull(_cursorIndexOfDeliveryPersonnelId)) {
              _tmpDeliveryPersonnelId = null;
            } else {
              _tmpDeliveryPersonnelId = _cursor.getInt(_cursorIndexOfDeliveryPersonnelId);
            }
            _result.setDeliveryPersonnelId(_tmpDeliveryPersonnelId);
            final Integer _tmpPromoCodeId;
            if (_cursor.isNull(_cursorIndexOfPromoCodeId)) {
              _tmpPromoCodeId = null;
            } else {
              _tmpPromoCodeId = _cursor.getInt(_cursorIndexOfPromoCodeId);
            }
            _result.setPromoCodeId(_tmpPromoCodeId);
            final double _tmpSubtotal;
            _tmpSubtotal = _cursor.getDouble(_cursorIndexOfSubtotal);
            _result.setSubtotal(_tmpSubtotal);
            final double _tmpDiscount;
            _tmpDiscount = _cursor.getDouble(_cursorIndexOfDiscount);
            _result.setDiscount(_tmpDiscount);
            final double _tmpDeliveryFee;
            _tmpDeliveryFee = _cursor.getDouble(_cursorIndexOfDeliveryFee);
            _result.setDeliveryFee(_tmpDeliveryFee);
            final double _tmpTotal;
            _tmpTotal = _cursor.getDouble(_cursorIndexOfTotal);
            _result.setTotal(_tmpTotal);
            final String _tmpPaymentMethod;
            if (_cursor.isNull(_cursorIndexOfPaymentMethod)) {
              _tmpPaymentMethod = null;
            } else {
              _tmpPaymentMethod = _cursor.getString(_cursorIndexOfPaymentMethod);
            }
            _result.setPaymentMethod(_tmpPaymentMethod);
            final String _tmpPaymentStatus;
            if (_cursor.isNull(_cursorIndexOfPaymentStatus)) {
              _tmpPaymentStatus = null;
            } else {
              _tmpPaymentStatus = _cursor.getString(_cursorIndexOfPaymentStatus);
            }
            _result.setPaymentStatus(_tmpPaymentStatus);
            final String _tmpStatus;
            if (_cursor.isNull(_cursorIndexOfStatus)) {
              _tmpStatus = null;
            } else {
              _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            }
            _result.setStatus(_tmpStatus);
            final String _tmpPickupAddress;
            if (_cursor.isNull(_cursorIndexOfPickupAddress)) {
              _tmpPickupAddress = null;
            } else {
              _tmpPickupAddress = _cursor.getString(_cursorIndexOfPickupAddress);
            }
            _result.setPickupAddress(_tmpPickupAddress);
            final Integer _tmpPickupDivisionId;
            if (_cursor.isNull(_cursorIndexOfPickupDivisionId)) {
              _tmpPickupDivisionId = null;
            } else {
              _tmpPickupDivisionId = _cursor.getInt(_cursorIndexOfPickupDivisionId);
            }
            _result.setPickupDivisionId(_tmpPickupDivisionId);
            final Integer _tmpPickupDistrictId;
            if (_cursor.isNull(_cursorIndexOfPickupDistrictId)) {
              _tmpPickupDistrictId = null;
            } else {
              _tmpPickupDistrictId = _cursor.getInt(_cursorIndexOfPickupDistrictId);
            }
            _result.setPickupDistrictId(_tmpPickupDistrictId);
            final Integer _tmpPickupUpazillaId;
            if (_cursor.isNull(_cursorIndexOfPickupUpazillaId)) {
              _tmpPickupUpazillaId = null;
            } else {
              _tmpPickupUpazillaId = _cursor.getInt(_cursorIndexOfPickupUpazillaId);
            }
            _result.setPickupUpazillaId(_tmpPickupUpazillaId);
            final Date _tmpPickupDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfPickupDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfPickupDate);
            }
            _tmpPickupDate = DateConverter.fromTimestamp(_tmp);
            _result.setPickupDate(_tmpPickupDate);
            final String _tmpPickupTimeSlot;
            if (_cursor.isNull(_cursorIndexOfPickupTimeSlot)) {
              _tmpPickupTimeSlot = null;
            } else {
              _tmpPickupTimeSlot = _cursor.getString(_cursorIndexOfPickupTimeSlot);
            }
            _result.setPickupTimeSlot(_tmpPickupTimeSlot);
            final String _tmpDeliveryAddress;
            if (_cursor.isNull(_cursorIndexOfDeliveryAddress)) {
              _tmpDeliveryAddress = null;
            } else {
              _tmpDeliveryAddress = _cursor.getString(_cursorIndexOfDeliveryAddress);
            }
            _result.setDeliveryAddress(_tmpDeliveryAddress);
            final Integer _tmpDeliveryDivisionId;
            if (_cursor.isNull(_cursorIndexOfDeliveryDivisionId)) {
              _tmpDeliveryDivisionId = null;
            } else {
              _tmpDeliveryDivisionId = _cursor.getInt(_cursorIndexOfDeliveryDivisionId);
            }
            _result.setDeliveryDivisionId(_tmpDeliveryDivisionId);
            final Integer _tmpDeliveryDistrictId;
            if (_cursor.isNull(_cursorIndexOfDeliveryDistrictId)) {
              _tmpDeliveryDistrictId = null;
            } else {
              _tmpDeliveryDistrictId = _cursor.getInt(_cursorIndexOfDeliveryDistrictId);
            }
            _result.setDeliveryDistrictId(_tmpDeliveryDistrictId);
            final Integer _tmpDeliveryUpazillaId;
            if (_cursor.isNull(_cursorIndexOfDeliveryUpazillaId)) {
              _tmpDeliveryUpazillaId = null;
            } else {
              _tmpDeliveryUpazillaId = _cursor.getInt(_cursorIndexOfDeliveryUpazillaId);
            }
            _result.setDeliveryUpazillaId(_tmpDeliveryUpazillaId);
            final Date _tmpDeliveryDate;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfDeliveryDate)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfDeliveryDate);
            }
            _tmpDeliveryDate = DateConverter.fromTimestamp(_tmp_1);
            _result.setDeliveryDate(_tmpDeliveryDate);
            final String _tmpDeliveryTimeSlot;
            if (_cursor.isNull(_cursorIndexOfDeliveryTimeSlot)) {
              _tmpDeliveryTimeSlot = null;
            } else {
              _tmpDeliveryTimeSlot = _cursor.getString(_cursorIndexOfDeliveryTimeSlot);
            }
            _result.setDeliveryTimeSlot(_tmpDeliveryTimeSlot);
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            _result.setNotes(_tmpNotes);
            final Date _tmpCreatedAt;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            _tmpCreatedAt = DateConverter.fromTimestamp(_tmp_2);
            _result.setCreatedAt(_tmpCreatedAt);
            final Date _tmpUpdatedAt;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            _tmpUpdatedAt = DateConverter.fromTimestamp(_tmp_3);
            _result.setUpdatedAt(_tmpUpdatedAt);
            final String _tmpCustomerName;
            if (_cursor.isNull(_cursorIndexOfCustomerName)) {
              _tmpCustomerName = null;
            } else {
              _tmpCustomerName = _cursor.getString(_cursorIndexOfCustomerName);
            }
            _result.setCustomerName(_tmpCustomerName);
            final String _tmpCustomerPhone;
            if (_cursor.isNull(_cursorIndexOfCustomerPhone)) {
              _tmpCustomerPhone = null;
            } else {
              _tmpCustomerPhone = _cursor.getString(_cursorIndexOfCustomerPhone);
            }
            _result.setCustomerPhone(_tmpCustomerPhone);
            final String _tmpDeliveryPersonName;
            if (_cursor.isNull(_cursorIndexOfDeliveryPersonName)) {
              _tmpDeliveryPersonName = null;
            } else {
              _tmpDeliveryPersonName = _cursor.getString(_cursorIndexOfDeliveryPersonName);
            }
            _result.setDeliveryPersonName(_tmpDeliveryPersonName);
            final String _tmpDeliveryPersonPhone;
            if (_cursor.isNull(_cursorIndexOfDeliveryPersonPhone)) {
              _tmpDeliveryPersonPhone = null;
            } else {
              _tmpDeliveryPersonPhone = _cursor.getString(_cursorIndexOfDeliveryPersonPhone);
            }
            _result.setDeliveryPersonPhone(_tmpDeliveryPersonPhone);
            final String _tmpPromoCode;
            if (_cursor.isNull(_cursorIndexOfPromoCode)) {
              _tmpPromoCode = null;
            } else {
              _tmpPromoCode = _cursor.getString(_cursorIndexOfPromoCode);
            }
            _result.setPromoCode(_tmpPromoCode);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public OrderEntity getOrderByOrderNumber(final String orderNumber) {
    final String _sql = "SELECT * FROM orders WHERE order_number = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (orderNumber == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, orderNumber);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfOrderNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "order_number");
      final int _cursorIndexOfTrackingNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "tracking_number");
      final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "user_id");
      final int _cursorIndexOfDeliveryPersonnelId = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_personnel_id");
      final int _cursorIndexOfPromoCodeId = CursorUtil.getColumnIndexOrThrow(_cursor, "promo_code_id");
      final int _cursorIndexOfSubtotal = CursorUtil.getColumnIndexOrThrow(_cursor, "subtotal");
      final int _cursorIndexOfDiscount = CursorUtil.getColumnIndexOrThrow(_cursor, "discount");
      final int _cursorIndexOfDeliveryFee = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_fee");
      final int _cursorIndexOfTotal = CursorUtil.getColumnIndexOrThrow(_cursor, "total");
      final int _cursorIndexOfPaymentMethod = CursorUtil.getColumnIndexOrThrow(_cursor, "payment_method");
      final int _cursorIndexOfPaymentStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "payment_status");
      final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
      final int _cursorIndexOfPickupAddress = CursorUtil.getColumnIndexOrThrow(_cursor, "pickup_address");
      final int _cursorIndexOfPickupDivisionId = CursorUtil.getColumnIndexOrThrow(_cursor, "pickup_division_id");
      final int _cursorIndexOfPickupDistrictId = CursorUtil.getColumnIndexOrThrow(_cursor, "pickup_district_id");
      final int _cursorIndexOfPickupUpazillaId = CursorUtil.getColumnIndexOrThrow(_cursor, "pickup_upazilla_id");
      final int _cursorIndexOfPickupDate = CursorUtil.getColumnIndexOrThrow(_cursor, "pickup_date");
      final int _cursorIndexOfPickupTimeSlot = CursorUtil.getColumnIndexOrThrow(_cursor, "pickup_time_slot");
      final int _cursorIndexOfDeliveryAddress = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_address");
      final int _cursorIndexOfDeliveryDivisionId = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_division_id");
      final int _cursorIndexOfDeliveryDistrictId = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_district_id");
      final int _cursorIndexOfDeliveryUpazillaId = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_upazilla_id");
      final int _cursorIndexOfDeliveryDate = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_date");
      final int _cursorIndexOfDeliveryTimeSlot = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_time_slot");
      final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
      final int _cursorIndexOfCustomerName = CursorUtil.getColumnIndexOrThrow(_cursor, "customerName");
      final int _cursorIndexOfCustomerPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "customerPhone");
      final int _cursorIndexOfDeliveryPersonName = CursorUtil.getColumnIndexOrThrow(_cursor, "deliveryPersonName");
      final int _cursorIndexOfDeliveryPersonPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "deliveryPersonPhone");
      final int _cursorIndexOfPromoCode = CursorUtil.getColumnIndexOrThrow(_cursor, "promoCode");
      final OrderEntity _result;
      if (_cursor.moveToFirst()) {
        _result = new OrderEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _result.setId(_tmpId);
        final String _tmpOrderNumber;
        if (_cursor.isNull(_cursorIndexOfOrderNumber)) {
          _tmpOrderNumber = null;
        } else {
          _tmpOrderNumber = _cursor.getString(_cursorIndexOfOrderNumber);
        }
        _result.setOrderNumber(_tmpOrderNumber);
        final String _tmpTrackingNumber;
        if (_cursor.isNull(_cursorIndexOfTrackingNumber)) {
          _tmpTrackingNumber = null;
        } else {
          _tmpTrackingNumber = _cursor.getString(_cursorIndexOfTrackingNumber);
        }
        _result.setTrackingNumber(_tmpTrackingNumber);
        final int _tmpUserId;
        _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
        _result.setUserId(_tmpUserId);
        final Integer _tmpDeliveryPersonnelId;
        if (_cursor.isNull(_cursorIndexOfDeliveryPersonnelId)) {
          _tmpDeliveryPersonnelId = null;
        } else {
          _tmpDeliveryPersonnelId = _cursor.getInt(_cursorIndexOfDeliveryPersonnelId);
        }
        _result.setDeliveryPersonnelId(_tmpDeliveryPersonnelId);
        final Integer _tmpPromoCodeId;
        if (_cursor.isNull(_cursorIndexOfPromoCodeId)) {
          _tmpPromoCodeId = null;
        } else {
          _tmpPromoCodeId = _cursor.getInt(_cursorIndexOfPromoCodeId);
        }
        _result.setPromoCodeId(_tmpPromoCodeId);
        final double _tmpSubtotal;
        _tmpSubtotal = _cursor.getDouble(_cursorIndexOfSubtotal);
        _result.setSubtotal(_tmpSubtotal);
        final double _tmpDiscount;
        _tmpDiscount = _cursor.getDouble(_cursorIndexOfDiscount);
        _result.setDiscount(_tmpDiscount);
        final double _tmpDeliveryFee;
        _tmpDeliveryFee = _cursor.getDouble(_cursorIndexOfDeliveryFee);
        _result.setDeliveryFee(_tmpDeliveryFee);
        final double _tmpTotal;
        _tmpTotal = _cursor.getDouble(_cursorIndexOfTotal);
        _result.setTotal(_tmpTotal);
        final String _tmpPaymentMethod;
        if (_cursor.isNull(_cursorIndexOfPaymentMethod)) {
          _tmpPaymentMethod = null;
        } else {
          _tmpPaymentMethod = _cursor.getString(_cursorIndexOfPaymentMethod);
        }
        _result.setPaymentMethod(_tmpPaymentMethod);
        final String _tmpPaymentStatus;
        if (_cursor.isNull(_cursorIndexOfPaymentStatus)) {
          _tmpPaymentStatus = null;
        } else {
          _tmpPaymentStatus = _cursor.getString(_cursorIndexOfPaymentStatus);
        }
        _result.setPaymentStatus(_tmpPaymentStatus);
        final String _tmpStatus;
        if (_cursor.isNull(_cursorIndexOfStatus)) {
          _tmpStatus = null;
        } else {
          _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
        }
        _result.setStatus(_tmpStatus);
        final String _tmpPickupAddress;
        if (_cursor.isNull(_cursorIndexOfPickupAddress)) {
          _tmpPickupAddress = null;
        } else {
          _tmpPickupAddress = _cursor.getString(_cursorIndexOfPickupAddress);
        }
        _result.setPickupAddress(_tmpPickupAddress);
        final Integer _tmpPickupDivisionId;
        if (_cursor.isNull(_cursorIndexOfPickupDivisionId)) {
          _tmpPickupDivisionId = null;
        } else {
          _tmpPickupDivisionId = _cursor.getInt(_cursorIndexOfPickupDivisionId);
        }
        _result.setPickupDivisionId(_tmpPickupDivisionId);
        final Integer _tmpPickupDistrictId;
        if (_cursor.isNull(_cursorIndexOfPickupDistrictId)) {
          _tmpPickupDistrictId = null;
        } else {
          _tmpPickupDistrictId = _cursor.getInt(_cursorIndexOfPickupDistrictId);
        }
        _result.setPickupDistrictId(_tmpPickupDistrictId);
        final Integer _tmpPickupUpazillaId;
        if (_cursor.isNull(_cursorIndexOfPickupUpazillaId)) {
          _tmpPickupUpazillaId = null;
        } else {
          _tmpPickupUpazillaId = _cursor.getInt(_cursorIndexOfPickupUpazillaId);
        }
        _result.setPickupUpazillaId(_tmpPickupUpazillaId);
        final Date _tmpPickupDate;
        final Long _tmp;
        if (_cursor.isNull(_cursorIndexOfPickupDate)) {
          _tmp = null;
        } else {
          _tmp = _cursor.getLong(_cursorIndexOfPickupDate);
        }
        _tmpPickupDate = DateConverter.fromTimestamp(_tmp);
        _result.setPickupDate(_tmpPickupDate);
        final String _tmpPickupTimeSlot;
        if (_cursor.isNull(_cursorIndexOfPickupTimeSlot)) {
          _tmpPickupTimeSlot = null;
        } else {
          _tmpPickupTimeSlot = _cursor.getString(_cursorIndexOfPickupTimeSlot);
        }
        _result.setPickupTimeSlot(_tmpPickupTimeSlot);
        final String _tmpDeliveryAddress;
        if (_cursor.isNull(_cursorIndexOfDeliveryAddress)) {
          _tmpDeliveryAddress = null;
        } else {
          _tmpDeliveryAddress = _cursor.getString(_cursorIndexOfDeliveryAddress);
        }
        _result.setDeliveryAddress(_tmpDeliveryAddress);
        final Integer _tmpDeliveryDivisionId;
        if (_cursor.isNull(_cursorIndexOfDeliveryDivisionId)) {
          _tmpDeliveryDivisionId = null;
        } else {
          _tmpDeliveryDivisionId = _cursor.getInt(_cursorIndexOfDeliveryDivisionId);
        }
        _result.setDeliveryDivisionId(_tmpDeliveryDivisionId);
        final Integer _tmpDeliveryDistrictId;
        if (_cursor.isNull(_cursorIndexOfDeliveryDistrictId)) {
          _tmpDeliveryDistrictId = null;
        } else {
          _tmpDeliveryDistrictId = _cursor.getInt(_cursorIndexOfDeliveryDistrictId);
        }
        _result.setDeliveryDistrictId(_tmpDeliveryDistrictId);
        final Integer _tmpDeliveryUpazillaId;
        if (_cursor.isNull(_cursorIndexOfDeliveryUpazillaId)) {
          _tmpDeliveryUpazillaId = null;
        } else {
          _tmpDeliveryUpazillaId = _cursor.getInt(_cursorIndexOfDeliveryUpazillaId);
        }
        _result.setDeliveryUpazillaId(_tmpDeliveryUpazillaId);
        final Date _tmpDeliveryDate;
        final Long _tmp_1;
        if (_cursor.isNull(_cursorIndexOfDeliveryDate)) {
          _tmp_1 = null;
        } else {
          _tmp_1 = _cursor.getLong(_cursorIndexOfDeliveryDate);
        }
        _tmpDeliveryDate = DateConverter.fromTimestamp(_tmp_1);
        _result.setDeliveryDate(_tmpDeliveryDate);
        final String _tmpDeliveryTimeSlot;
        if (_cursor.isNull(_cursorIndexOfDeliveryTimeSlot)) {
          _tmpDeliveryTimeSlot = null;
        } else {
          _tmpDeliveryTimeSlot = _cursor.getString(_cursorIndexOfDeliveryTimeSlot);
        }
        _result.setDeliveryTimeSlot(_tmpDeliveryTimeSlot);
        final String _tmpNotes;
        if (_cursor.isNull(_cursorIndexOfNotes)) {
          _tmpNotes = null;
        } else {
          _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
        }
        _result.setNotes(_tmpNotes);
        final Date _tmpCreatedAt;
        final Long _tmp_2;
        if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
          _tmp_2 = null;
        } else {
          _tmp_2 = _cursor.getLong(_cursorIndexOfCreatedAt);
        }
        _tmpCreatedAt = DateConverter.fromTimestamp(_tmp_2);
        _result.setCreatedAt(_tmpCreatedAt);
        final Date _tmpUpdatedAt;
        final Long _tmp_3;
        if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
          _tmp_3 = null;
        } else {
          _tmp_3 = _cursor.getLong(_cursorIndexOfUpdatedAt);
        }
        _tmpUpdatedAt = DateConverter.fromTimestamp(_tmp_3);
        _result.setUpdatedAt(_tmpUpdatedAt);
        final String _tmpCustomerName;
        if (_cursor.isNull(_cursorIndexOfCustomerName)) {
          _tmpCustomerName = null;
        } else {
          _tmpCustomerName = _cursor.getString(_cursorIndexOfCustomerName);
        }
        _result.setCustomerName(_tmpCustomerName);
        final String _tmpCustomerPhone;
        if (_cursor.isNull(_cursorIndexOfCustomerPhone)) {
          _tmpCustomerPhone = null;
        } else {
          _tmpCustomerPhone = _cursor.getString(_cursorIndexOfCustomerPhone);
        }
        _result.setCustomerPhone(_tmpCustomerPhone);
        final String _tmpDeliveryPersonName;
        if (_cursor.isNull(_cursorIndexOfDeliveryPersonName)) {
          _tmpDeliveryPersonName = null;
        } else {
          _tmpDeliveryPersonName = _cursor.getString(_cursorIndexOfDeliveryPersonName);
        }
        _result.setDeliveryPersonName(_tmpDeliveryPersonName);
        final String _tmpDeliveryPersonPhone;
        if (_cursor.isNull(_cursorIndexOfDeliveryPersonPhone)) {
          _tmpDeliveryPersonPhone = null;
        } else {
          _tmpDeliveryPersonPhone = _cursor.getString(_cursorIndexOfDeliveryPersonPhone);
        }
        _result.setDeliveryPersonPhone(_tmpDeliveryPersonPhone);
        final String _tmpPromoCode;
        if (_cursor.isNull(_cursorIndexOfPromoCode)) {
          _tmpPromoCode = null;
        } else {
          _tmpPromoCode = _cursor.getString(_cursorIndexOfPromoCode);
        }
        _result.setPromoCode(_tmpPromoCode);
      } else {
        _result = null;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public OrderEntity getOrderByTrackingNumber(final String trackingNumber) {
    final String _sql = "SELECT * FROM orders WHERE tracking_number = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (trackingNumber == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, trackingNumber);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfOrderNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "order_number");
      final int _cursorIndexOfTrackingNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "tracking_number");
      final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "user_id");
      final int _cursorIndexOfDeliveryPersonnelId = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_personnel_id");
      final int _cursorIndexOfPromoCodeId = CursorUtil.getColumnIndexOrThrow(_cursor, "promo_code_id");
      final int _cursorIndexOfSubtotal = CursorUtil.getColumnIndexOrThrow(_cursor, "subtotal");
      final int _cursorIndexOfDiscount = CursorUtil.getColumnIndexOrThrow(_cursor, "discount");
      final int _cursorIndexOfDeliveryFee = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_fee");
      final int _cursorIndexOfTotal = CursorUtil.getColumnIndexOrThrow(_cursor, "total");
      final int _cursorIndexOfPaymentMethod = CursorUtil.getColumnIndexOrThrow(_cursor, "payment_method");
      final int _cursorIndexOfPaymentStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "payment_status");
      final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
      final int _cursorIndexOfPickupAddress = CursorUtil.getColumnIndexOrThrow(_cursor, "pickup_address");
      final int _cursorIndexOfPickupDivisionId = CursorUtil.getColumnIndexOrThrow(_cursor, "pickup_division_id");
      final int _cursorIndexOfPickupDistrictId = CursorUtil.getColumnIndexOrThrow(_cursor, "pickup_district_id");
      final int _cursorIndexOfPickupUpazillaId = CursorUtil.getColumnIndexOrThrow(_cursor, "pickup_upazilla_id");
      final int _cursorIndexOfPickupDate = CursorUtil.getColumnIndexOrThrow(_cursor, "pickup_date");
      final int _cursorIndexOfPickupTimeSlot = CursorUtil.getColumnIndexOrThrow(_cursor, "pickup_time_slot");
      final int _cursorIndexOfDeliveryAddress = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_address");
      final int _cursorIndexOfDeliveryDivisionId = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_division_id");
      final int _cursorIndexOfDeliveryDistrictId = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_district_id");
      final int _cursorIndexOfDeliveryUpazillaId = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_upazilla_id");
      final int _cursorIndexOfDeliveryDate = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_date");
      final int _cursorIndexOfDeliveryTimeSlot = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_time_slot");
      final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
      final int _cursorIndexOfCustomerName = CursorUtil.getColumnIndexOrThrow(_cursor, "customerName");
      final int _cursorIndexOfCustomerPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "customerPhone");
      final int _cursorIndexOfDeliveryPersonName = CursorUtil.getColumnIndexOrThrow(_cursor, "deliveryPersonName");
      final int _cursorIndexOfDeliveryPersonPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "deliveryPersonPhone");
      final int _cursorIndexOfPromoCode = CursorUtil.getColumnIndexOrThrow(_cursor, "promoCode");
      final OrderEntity _result;
      if (_cursor.moveToFirst()) {
        _result = new OrderEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _result.setId(_tmpId);
        final String _tmpOrderNumber;
        if (_cursor.isNull(_cursorIndexOfOrderNumber)) {
          _tmpOrderNumber = null;
        } else {
          _tmpOrderNumber = _cursor.getString(_cursorIndexOfOrderNumber);
        }
        _result.setOrderNumber(_tmpOrderNumber);
        final String _tmpTrackingNumber;
        if (_cursor.isNull(_cursorIndexOfTrackingNumber)) {
          _tmpTrackingNumber = null;
        } else {
          _tmpTrackingNumber = _cursor.getString(_cursorIndexOfTrackingNumber);
        }
        _result.setTrackingNumber(_tmpTrackingNumber);
        final int _tmpUserId;
        _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
        _result.setUserId(_tmpUserId);
        final Integer _tmpDeliveryPersonnelId;
        if (_cursor.isNull(_cursorIndexOfDeliveryPersonnelId)) {
          _tmpDeliveryPersonnelId = null;
        } else {
          _tmpDeliveryPersonnelId = _cursor.getInt(_cursorIndexOfDeliveryPersonnelId);
        }
        _result.setDeliveryPersonnelId(_tmpDeliveryPersonnelId);
        final Integer _tmpPromoCodeId;
        if (_cursor.isNull(_cursorIndexOfPromoCodeId)) {
          _tmpPromoCodeId = null;
        } else {
          _tmpPromoCodeId = _cursor.getInt(_cursorIndexOfPromoCodeId);
        }
        _result.setPromoCodeId(_tmpPromoCodeId);
        final double _tmpSubtotal;
        _tmpSubtotal = _cursor.getDouble(_cursorIndexOfSubtotal);
        _result.setSubtotal(_tmpSubtotal);
        final double _tmpDiscount;
        _tmpDiscount = _cursor.getDouble(_cursorIndexOfDiscount);
        _result.setDiscount(_tmpDiscount);
        final double _tmpDeliveryFee;
        _tmpDeliveryFee = _cursor.getDouble(_cursorIndexOfDeliveryFee);
        _result.setDeliveryFee(_tmpDeliveryFee);
        final double _tmpTotal;
        _tmpTotal = _cursor.getDouble(_cursorIndexOfTotal);
        _result.setTotal(_tmpTotal);
        final String _tmpPaymentMethod;
        if (_cursor.isNull(_cursorIndexOfPaymentMethod)) {
          _tmpPaymentMethod = null;
        } else {
          _tmpPaymentMethod = _cursor.getString(_cursorIndexOfPaymentMethod);
        }
        _result.setPaymentMethod(_tmpPaymentMethod);
        final String _tmpPaymentStatus;
        if (_cursor.isNull(_cursorIndexOfPaymentStatus)) {
          _tmpPaymentStatus = null;
        } else {
          _tmpPaymentStatus = _cursor.getString(_cursorIndexOfPaymentStatus);
        }
        _result.setPaymentStatus(_tmpPaymentStatus);
        final String _tmpStatus;
        if (_cursor.isNull(_cursorIndexOfStatus)) {
          _tmpStatus = null;
        } else {
          _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
        }
        _result.setStatus(_tmpStatus);
        final String _tmpPickupAddress;
        if (_cursor.isNull(_cursorIndexOfPickupAddress)) {
          _tmpPickupAddress = null;
        } else {
          _tmpPickupAddress = _cursor.getString(_cursorIndexOfPickupAddress);
        }
        _result.setPickupAddress(_tmpPickupAddress);
        final Integer _tmpPickupDivisionId;
        if (_cursor.isNull(_cursorIndexOfPickupDivisionId)) {
          _tmpPickupDivisionId = null;
        } else {
          _tmpPickupDivisionId = _cursor.getInt(_cursorIndexOfPickupDivisionId);
        }
        _result.setPickupDivisionId(_tmpPickupDivisionId);
        final Integer _tmpPickupDistrictId;
        if (_cursor.isNull(_cursorIndexOfPickupDistrictId)) {
          _tmpPickupDistrictId = null;
        } else {
          _tmpPickupDistrictId = _cursor.getInt(_cursorIndexOfPickupDistrictId);
        }
        _result.setPickupDistrictId(_tmpPickupDistrictId);
        final Integer _tmpPickupUpazillaId;
        if (_cursor.isNull(_cursorIndexOfPickupUpazillaId)) {
          _tmpPickupUpazillaId = null;
        } else {
          _tmpPickupUpazillaId = _cursor.getInt(_cursorIndexOfPickupUpazillaId);
        }
        _result.setPickupUpazillaId(_tmpPickupUpazillaId);
        final Date _tmpPickupDate;
        final Long _tmp;
        if (_cursor.isNull(_cursorIndexOfPickupDate)) {
          _tmp = null;
        } else {
          _tmp = _cursor.getLong(_cursorIndexOfPickupDate);
        }
        _tmpPickupDate = DateConverter.fromTimestamp(_tmp);
        _result.setPickupDate(_tmpPickupDate);
        final String _tmpPickupTimeSlot;
        if (_cursor.isNull(_cursorIndexOfPickupTimeSlot)) {
          _tmpPickupTimeSlot = null;
        } else {
          _tmpPickupTimeSlot = _cursor.getString(_cursorIndexOfPickupTimeSlot);
        }
        _result.setPickupTimeSlot(_tmpPickupTimeSlot);
        final String _tmpDeliveryAddress;
        if (_cursor.isNull(_cursorIndexOfDeliveryAddress)) {
          _tmpDeliveryAddress = null;
        } else {
          _tmpDeliveryAddress = _cursor.getString(_cursorIndexOfDeliveryAddress);
        }
        _result.setDeliveryAddress(_tmpDeliveryAddress);
        final Integer _tmpDeliveryDivisionId;
        if (_cursor.isNull(_cursorIndexOfDeliveryDivisionId)) {
          _tmpDeliveryDivisionId = null;
        } else {
          _tmpDeliveryDivisionId = _cursor.getInt(_cursorIndexOfDeliveryDivisionId);
        }
        _result.setDeliveryDivisionId(_tmpDeliveryDivisionId);
        final Integer _tmpDeliveryDistrictId;
        if (_cursor.isNull(_cursorIndexOfDeliveryDistrictId)) {
          _tmpDeliveryDistrictId = null;
        } else {
          _tmpDeliveryDistrictId = _cursor.getInt(_cursorIndexOfDeliveryDistrictId);
        }
        _result.setDeliveryDistrictId(_tmpDeliveryDistrictId);
        final Integer _tmpDeliveryUpazillaId;
        if (_cursor.isNull(_cursorIndexOfDeliveryUpazillaId)) {
          _tmpDeliveryUpazillaId = null;
        } else {
          _tmpDeliveryUpazillaId = _cursor.getInt(_cursorIndexOfDeliveryUpazillaId);
        }
        _result.setDeliveryUpazillaId(_tmpDeliveryUpazillaId);
        final Date _tmpDeliveryDate;
        final Long _tmp_1;
        if (_cursor.isNull(_cursorIndexOfDeliveryDate)) {
          _tmp_1 = null;
        } else {
          _tmp_1 = _cursor.getLong(_cursorIndexOfDeliveryDate);
        }
        _tmpDeliveryDate = DateConverter.fromTimestamp(_tmp_1);
        _result.setDeliveryDate(_tmpDeliveryDate);
        final String _tmpDeliveryTimeSlot;
        if (_cursor.isNull(_cursorIndexOfDeliveryTimeSlot)) {
          _tmpDeliveryTimeSlot = null;
        } else {
          _tmpDeliveryTimeSlot = _cursor.getString(_cursorIndexOfDeliveryTimeSlot);
        }
        _result.setDeliveryTimeSlot(_tmpDeliveryTimeSlot);
        final String _tmpNotes;
        if (_cursor.isNull(_cursorIndexOfNotes)) {
          _tmpNotes = null;
        } else {
          _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
        }
        _result.setNotes(_tmpNotes);
        final Date _tmpCreatedAt;
        final Long _tmp_2;
        if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
          _tmp_2 = null;
        } else {
          _tmp_2 = _cursor.getLong(_cursorIndexOfCreatedAt);
        }
        _tmpCreatedAt = DateConverter.fromTimestamp(_tmp_2);
        _result.setCreatedAt(_tmpCreatedAt);
        final Date _tmpUpdatedAt;
        final Long _tmp_3;
        if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
          _tmp_3 = null;
        } else {
          _tmp_3 = _cursor.getLong(_cursorIndexOfUpdatedAt);
        }
        _tmpUpdatedAt = DateConverter.fromTimestamp(_tmp_3);
        _result.setUpdatedAt(_tmpUpdatedAt);
        final String _tmpCustomerName;
        if (_cursor.isNull(_cursorIndexOfCustomerName)) {
          _tmpCustomerName = null;
        } else {
          _tmpCustomerName = _cursor.getString(_cursorIndexOfCustomerName);
        }
        _result.setCustomerName(_tmpCustomerName);
        final String _tmpCustomerPhone;
        if (_cursor.isNull(_cursorIndexOfCustomerPhone)) {
          _tmpCustomerPhone = null;
        } else {
          _tmpCustomerPhone = _cursor.getString(_cursorIndexOfCustomerPhone);
        }
        _result.setCustomerPhone(_tmpCustomerPhone);
        final String _tmpDeliveryPersonName;
        if (_cursor.isNull(_cursorIndexOfDeliveryPersonName)) {
          _tmpDeliveryPersonName = null;
        } else {
          _tmpDeliveryPersonName = _cursor.getString(_cursorIndexOfDeliveryPersonName);
        }
        _result.setDeliveryPersonName(_tmpDeliveryPersonName);
        final String _tmpDeliveryPersonPhone;
        if (_cursor.isNull(_cursorIndexOfDeliveryPersonPhone)) {
          _tmpDeliveryPersonPhone = null;
        } else {
          _tmpDeliveryPersonPhone = _cursor.getString(_cursorIndexOfDeliveryPersonPhone);
        }
        _result.setDeliveryPersonPhone(_tmpDeliveryPersonPhone);
        final String _tmpPromoCode;
        if (_cursor.isNull(_cursorIndexOfPromoCode)) {
          _tmpPromoCode = null;
        } else {
          _tmpPromoCode = _cursor.getString(_cursorIndexOfPromoCode);
        }
        _result.setPromoCode(_tmpPromoCode);
      } else {
        _result = null;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public List<OrderItemEntity> getOrderItemsByOrderId(final int orderId) {
    final String _sql = "SELECT * FROM order_items WHERE order_id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, orderId);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfOrderId = CursorUtil.getColumnIndexOrThrow(_cursor, "order_id");
      final int _cursorIndexOfItemId = CursorUtil.getColumnIndexOrThrow(_cursor, "item_id");
      final int _cursorIndexOfQuantity = CursorUtil.getColumnIndexOrThrow(_cursor, "quantity");
      final int _cursorIndexOfPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "price");
      final int _cursorIndexOfSubtotal = CursorUtil.getColumnIndexOrThrow(_cursor, "subtotal");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
      final int _cursorIndexOfItemName = CursorUtil.getColumnIndexOrThrow(_cursor, "itemName");
      final int _cursorIndexOfItemBnName = CursorUtil.getColumnIndexOrThrow(_cursor, "itemBnName");
      final int _cursorIndexOfItemImageUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "itemImageUrl");
      final int _cursorIndexOfServiceName = CursorUtil.getColumnIndexOrThrow(_cursor, "serviceName");
      final int _cursorIndexOfServiceBnName = CursorUtil.getColumnIndexOrThrow(_cursor, "serviceBnName");
      final List<OrderItemEntity> _result = new ArrayList<OrderItemEntity>(_cursor.getCount());
      while (_cursor.moveToNext()) {
        final OrderItemEntity _item;
        _item = new OrderItemEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _item.setId(_tmpId);
        final int _tmpOrderId;
        _tmpOrderId = _cursor.getInt(_cursorIndexOfOrderId);
        _item.setOrderId(_tmpOrderId);
        final int _tmpItemId;
        _tmpItemId = _cursor.getInt(_cursorIndexOfItemId);
        _item.setItemId(_tmpItemId);
        final int _tmpQuantity;
        _tmpQuantity = _cursor.getInt(_cursorIndexOfQuantity);
        _item.setQuantity(_tmpQuantity);
        final double _tmpPrice;
        _tmpPrice = _cursor.getDouble(_cursorIndexOfPrice);
        _item.setPrice(_tmpPrice);
        final double _tmpSubtotal;
        _tmpSubtotal = _cursor.getDouble(_cursorIndexOfSubtotal);
        _item.setSubtotal(_tmpSubtotal);
        final Date _tmpCreatedAt;
        final Long _tmp;
        if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
          _tmp = null;
        } else {
          _tmp = _cursor.getLong(_cursorIndexOfCreatedAt);
        }
        _tmpCreatedAt = DateConverter.fromTimestamp(_tmp);
        _item.setCreatedAt(_tmpCreatedAt);
        final Date _tmpUpdatedAt;
        final Long _tmp_1;
        if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
          _tmp_1 = null;
        } else {
          _tmp_1 = _cursor.getLong(_cursorIndexOfUpdatedAt);
        }
        _tmpUpdatedAt = DateConverter.fromTimestamp(_tmp_1);
        _item.setUpdatedAt(_tmpUpdatedAt);
        final String _tmpItemName;
        if (_cursor.isNull(_cursorIndexOfItemName)) {
          _tmpItemName = null;
        } else {
          _tmpItemName = _cursor.getString(_cursorIndexOfItemName);
        }
        _item.setItemName(_tmpItemName);
        final String _tmpItemBnName;
        if (_cursor.isNull(_cursorIndexOfItemBnName)) {
          _tmpItemBnName = null;
        } else {
          _tmpItemBnName = _cursor.getString(_cursorIndexOfItemBnName);
        }
        _item.setItemBnName(_tmpItemBnName);
        final String _tmpItemImageUrl;
        if (_cursor.isNull(_cursorIndexOfItemImageUrl)) {
          _tmpItemImageUrl = null;
        } else {
          _tmpItemImageUrl = _cursor.getString(_cursorIndexOfItemImageUrl);
        }
        _item.setItemImageUrl(_tmpItemImageUrl);
        final String _tmpServiceName;
        if (_cursor.isNull(_cursorIndexOfServiceName)) {
          _tmpServiceName = null;
        } else {
          _tmpServiceName = _cursor.getString(_cursorIndexOfServiceName);
        }
        _item.setServiceName(_tmpServiceName);
        final String _tmpServiceBnName;
        if (_cursor.isNull(_cursorIndexOfServiceBnName)) {
          _tmpServiceBnName = null;
        } else {
          _tmpServiceBnName = _cursor.getString(_cursorIndexOfServiceBnName);
        }
        _item.setServiceBnName(_tmpServiceBnName);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public LiveData<List<OrderItemEntity>> getOrderItemsByOrderIdLive(final int orderId) {
    final String _sql = "SELECT * FROM order_items WHERE order_id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, orderId);
    return __db.getInvalidationTracker().createLiveData(new String[] {"order_items"}, false, new Callable<List<OrderItemEntity>>() {
      @Override
      @Nullable
      public List<OrderItemEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfOrderId = CursorUtil.getColumnIndexOrThrow(_cursor, "order_id");
          final int _cursorIndexOfItemId = CursorUtil.getColumnIndexOrThrow(_cursor, "item_id");
          final int _cursorIndexOfQuantity = CursorUtil.getColumnIndexOrThrow(_cursor, "quantity");
          final int _cursorIndexOfPrice = CursorUtil.getColumnIndexOrThrow(_cursor, "price");
          final int _cursorIndexOfSubtotal = CursorUtil.getColumnIndexOrThrow(_cursor, "subtotal");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final int _cursorIndexOfItemName = CursorUtil.getColumnIndexOrThrow(_cursor, "itemName");
          final int _cursorIndexOfItemBnName = CursorUtil.getColumnIndexOrThrow(_cursor, "itemBnName");
          final int _cursorIndexOfItemImageUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "itemImageUrl");
          final int _cursorIndexOfServiceName = CursorUtil.getColumnIndexOrThrow(_cursor, "serviceName");
          final int _cursorIndexOfServiceBnName = CursorUtil.getColumnIndexOrThrow(_cursor, "serviceBnName");
          final List<OrderItemEntity> _result = new ArrayList<OrderItemEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final OrderItemEntity _item;
            _item = new OrderItemEntity();
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            _item.setId(_tmpId);
            final int _tmpOrderId;
            _tmpOrderId = _cursor.getInt(_cursorIndexOfOrderId);
            _item.setOrderId(_tmpOrderId);
            final int _tmpItemId;
            _tmpItemId = _cursor.getInt(_cursorIndexOfItemId);
            _item.setItemId(_tmpItemId);
            final int _tmpQuantity;
            _tmpQuantity = _cursor.getInt(_cursorIndexOfQuantity);
            _item.setQuantity(_tmpQuantity);
            final double _tmpPrice;
            _tmpPrice = _cursor.getDouble(_cursorIndexOfPrice);
            _item.setPrice(_tmpPrice);
            final double _tmpSubtotal;
            _tmpSubtotal = _cursor.getDouble(_cursorIndexOfSubtotal);
            _item.setSubtotal(_tmpSubtotal);
            final Date _tmpCreatedAt;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            _tmpCreatedAt = DateConverter.fromTimestamp(_tmp);
            _item.setCreatedAt(_tmpCreatedAt);
            final Date _tmpUpdatedAt;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            _tmpUpdatedAt = DateConverter.fromTimestamp(_tmp_1);
            _item.setUpdatedAt(_tmpUpdatedAt);
            final String _tmpItemName;
            if (_cursor.isNull(_cursorIndexOfItemName)) {
              _tmpItemName = null;
            } else {
              _tmpItemName = _cursor.getString(_cursorIndexOfItemName);
            }
            _item.setItemName(_tmpItemName);
            final String _tmpItemBnName;
            if (_cursor.isNull(_cursorIndexOfItemBnName)) {
              _tmpItemBnName = null;
            } else {
              _tmpItemBnName = _cursor.getString(_cursorIndexOfItemBnName);
            }
            _item.setItemBnName(_tmpItemBnName);
            final String _tmpItemImageUrl;
            if (_cursor.isNull(_cursorIndexOfItemImageUrl)) {
              _tmpItemImageUrl = null;
            } else {
              _tmpItemImageUrl = _cursor.getString(_cursorIndexOfItemImageUrl);
            }
            _item.setItemImageUrl(_tmpItemImageUrl);
            final String _tmpServiceName;
            if (_cursor.isNull(_cursorIndexOfServiceName)) {
              _tmpServiceName = null;
            } else {
              _tmpServiceName = _cursor.getString(_cursorIndexOfServiceName);
            }
            _item.setServiceName(_tmpServiceName);
            final String _tmpServiceBnName;
            if (_cursor.isNull(_cursorIndexOfServiceBnName)) {
              _tmpServiceBnName = null;
            } else {
              _tmpServiceBnName = _cursor.getString(_cursorIndexOfServiceBnName);
            }
            _item.setServiceBnName(_tmpServiceBnName);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public List<OrderEntity> getOrdersByUserId(final int userId) {
    final String _sql = "SELECT * FROM orders WHERE user_id = ? ORDER BY created_at DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, userId);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfOrderNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "order_number");
      final int _cursorIndexOfTrackingNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "tracking_number");
      final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "user_id");
      final int _cursorIndexOfDeliveryPersonnelId = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_personnel_id");
      final int _cursorIndexOfPromoCodeId = CursorUtil.getColumnIndexOrThrow(_cursor, "promo_code_id");
      final int _cursorIndexOfSubtotal = CursorUtil.getColumnIndexOrThrow(_cursor, "subtotal");
      final int _cursorIndexOfDiscount = CursorUtil.getColumnIndexOrThrow(_cursor, "discount");
      final int _cursorIndexOfDeliveryFee = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_fee");
      final int _cursorIndexOfTotal = CursorUtil.getColumnIndexOrThrow(_cursor, "total");
      final int _cursorIndexOfPaymentMethod = CursorUtil.getColumnIndexOrThrow(_cursor, "payment_method");
      final int _cursorIndexOfPaymentStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "payment_status");
      final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
      final int _cursorIndexOfPickupAddress = CursorUtil.getColumnIndexOrThrow(_cursor, "pickup_address");
      final int _cursorIndexOfPickupDivisionId = CursorUtil.getColumnIndexOrThrow(_cursor, "pickup_division_id");
      final int _cursorIndexOfPickupDistrictId = CursorUtil.getColumnIndexOrThrow(_cursor, "pickup_district_id");
      final int _cursorIndexOfPickupUpazillaId = CursorUtil.getColumnIndexOrThrow(_cursor, "pickup_upazilla_id");
      final int _cursorIndexOfPickupDate = CursorUtil.getColumnIndexOrThrow(_cursor, "pickup_date");
      final int _cursorIndexOfPickupTimeSlot = CursorUtil.getColumnIndexOrThrow(_cursor, "pickup_time_slot");
      final int _cursorIndexOfDeliveryAddress = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_address");
      final int _cursorIndexOfDeliveryDivisionId = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_division_id");
      final int _cursorIndexOfDeliveryDistrictId = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_district_id");
      final int _cursorIndexOfDeliveryUpazillaId = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_upazilla_id");
      final int _cursorIndexOfDeliveryDate = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_date");
      final int _cursorIndexOfDeliveryTimeSlot = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_time_slot");
      final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
      final int _cursorIndexOfCustomerName = CursorUtil.getColumnIndexOrThrow(_cursor, "customerName");
      final int _cursorIndexOfCustomerPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "customerPhone");
      final int _cursorIndexOfDeliveryPersonName = CursorUtil.getColumnIndexOrThrow(_cursor, "deliveryPersonName");
      final int _cursorIndexOfDeliveryPersonPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "deliveryPersonPhone");
      final int _cursorIndexOfPromoCode = CursorUtil.getColumnIndexOrThrow(_cursor, "promoCode");
      final List<OrderEntity> _result = new ArrayList<OrderEntity>(_cursor.getCount());
      while (_cursor.moveToNext()) {
        final OrderEntity _item;
        _item = new OrderEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _item.setId(_tmpId);
        final String _tmpOrderNumber;
        if (_cursor.isNull(_cursorIndexOfOrderNumber)) {
          _tmpOrderNumber = null;
        } else {
          _tmpOrderNumber = _cursor.getString(_cursorIndexOfOrderNumber);
        }
        _item.setOrderNumber(_tmpOrderNumber);
        final String _tmpTrackingNumber;
        if (_cursor.isNull(_cursorIndexOfTrackingNumber)) {
          _tmpTrackingNumber = null;
        } else {
          _tmpTrackingNumber = _cursor.getString(_cursorIndexOfTrackingNumber);
        }
        _item.setTrackingNumber(_tmpTrackingNumber);
        final int _tmpUserId;
        _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
        _item.setUserId(_tmpUserId);
        final Integer _tmpDeliveryPersonnelId;
        if (_cursor.isNull(_cursorIndexOfDeliveryPersonnelId)) {
          _tmpDeliveryPersonnelId = null;
        } else {
          _tmpDeliveryPersonnelId = _cursor.getInt(_cursorIndexOfDeliveryPersonnelId);
        }
        _item.setDeliveryPersonnelId(_tmpDeliveryPersonnelId);
        final Integer _tmpPromoCodeId;
        if (_cursor.isNull(_cursorIndexOfPromoCodeId)) {
          _tmpPromoCodeId = null;
        } else {
          _tmpPromoCodeId = _cursor.getInt(_cursorIndexOfPromoCodeId);
        }
        _item.setPromoCodeId(_tmpPromoCodeId);
        final double _tmpSubtotal;
        _tmpSubtotal = _cursor.getDouble(_cursorIndexOfSubtotal);
        _item.setSubtotal(_tmpSubtotal);
        final double _tmpDiscount;
        _tmpDiscount = _cursor.getDouble(_cursorIndexOfDiscount);
        _item.setDiscount(_tmpDiscount);
        final double _tmpDeliveryFee;
        _tmpDeliveryFee = _cursor.getDouble(_cursorIndexOfDeliveryFee);
        _item.setDeliveryFee(_tmpDeliveryFee);
        final double _tmpTotal;
        _tmpTotal = _cursor.getDouble(_cursorIndexOfTotal);
        _item.setTotal(_tmpTotal);
        final String _tmpPaymentMethod;
        if (_cursor.isNull(_cursorIndexOfPaymentMethod)) {
          _tmpPaymentMethod = null;
        } else {
          _tmpPaymentMethod = _cursor.getString(_cursorIndexOfPaymentMethod);
        }
        _item.setPaymentMethod(_tmpPaymentMethod);
        final String _tmpPaymentStatus;
        if (_cursor.isNull(_cursorIndexOfPaymentStatus)) {
          _tmpPaymentStatus = null;
        } else {
          _tmpPaymentStatus = _cursor.getString(_cursorIndexOfPaymentStatus);
        }
        _item.setPaymentStatus(_tmpPaymentStatus);
        final String _tmpStatus;
        if (_cursor.isNull(_cursorIndexOfStatus)) {
          _tmpStatus = null;
        } else {
          _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
        }
        _item.setStatus(_tmpStatus);
        final String _tmpPickupAddress;
        if (_cursor.isNull(_cursorIndexOfPickupAddress)) {
          _tmpPickupAddress = null;
        } else {
          _tmpPickupAddress = _cursor.getString(_cursorIndexOfPickupAddress);
        }
        _item.setPickupAddress(_tmpPickupAddress);
        final Integer _tmpPickupDivisionId;
        if (_cursor.isNull(_cursorIndexOfPickupDivisionId)) {
          _tmpPickupDivisionId = null;
        } else {
          _tmpPickupDivisionId = _cursor.getInt(_cursorIndexOfPickupDivisionId);
        }
        _item.setPickupDivisionId(_tmpPickupDivisionId);
        final Integer _tmpPickupDistrictId;
        if (_cursor.isNull(_cursorIndexOfPickupDistrictId)) {
          _tmpPickupDistrictId = null;
        } else {
          _tmpPickupDistrictId = _cursor.getInt(_cursorIndexOfPickupDistrictId);
        }
        _item.setPickupDistrictId(_tmpPickupDistrictId);
        final Integer _tmpPickupUpazillaId;
        if (_cursor.isNull(_cursorIndexOfPickupUpazillaId)) {
          _tmpPickupUpazillaId = null;
        } else {
          _tmpPickupUpazillaId = _cursor.getInt(_cursorIndexOfPickupUpazillaId);
        }
        _item.setPickupUpazillaId(_tmpPickupUpazillaId);
        final Date _tmpPickupDate;
        final Long _tmp;
        if (_cursor.isNull(_cursorIndexOfPickupDate)) {
          _tmp = null;
        } else {
          _tmp = _cursor.getLong(_cursorIndexOfPickupDate);
        }
        _tmpPickupDate = DateConverter.fromTimestamp(_tmp);
        _item.setPickupDate(_tmpPickupDate);
        final String _tmpPickupTimeSlot;
        if (_cursor.isNull(_cursorIndexOfPickupTimeSlot)) {
          _tmpPickupTimeSlot = null;
        } else {
          _tmpPickupTimeSlot = _cursor.getString(_cursorIndexOfPickupTimeSlot);
        }
        _item.setPickupTimeSlot(_tmpPickupTimeSlot);
        final String _tmpDeliveryAddress;
        if (_cursor.isNull(_cursorIndexOfDeliveryAddress)) {
          _tmpDeliveryAddress = null;
        } else {
          _tmpDeliveryAddress = _cursor.getString(_cursorIndexOfDeliveryAddress);
        }
        _item.setDeliveryAddress(_tmpDeliveryAddress);
        final Integer _tmpDeliveryDivisionId;
        if (_cursor.isNull(_cursorIndexOfDeliveryDivisionId)) {
          _tmpDeliveryDivisionId = null;
        } else {
          _tmpDeliveryDivisionId = _cursor.getInt(_cursorIndexOfDeliveryDivisionId);
        }
        _item.setDeliveryDivisionId(_tmpDeliveryDivisionId);
        final Integer _tmpDeliveryDistrictId;
        if (_cursor.isNull(_cursorIndexOfDeliveryDistrictId)) {
          _tmpDeliveryDistrictId = null;
        } else {
          _tmpDeliveryDistrictId = _cursor.getInt(_cursorIndexOfDeliveryDistrictId);
        }
        _item.setDeliveryDistrictId(_tmpDeliveryDistrictId);
        final Integer _tmpDeliveryUpazillaId;
        if (_cursor.isNull(_cursorIndexOfDeliveryUpazillaId)) {
          _tmpDeliveryUpazillaId = null;
        } else {
          _tmpDeliveryUpazillaId = _cursor.getInt(_cursorIndexOfDeliveryUpazillaId);
        }
        _item.setDeliveryUpazillaId(_tmpDeliveryUpazillaId);
        final Date _tmpDeliveryDate;
        final Long _tmp_1;
        if (_cursor.isNull(_cursorIndexOfDeliveryDate)) {
          _tmp_1 = null;
        } else {
          _tmp_1 = _cursor.getLong(_cursorIndexOfDeliveryDate);
        }
        _tmpDeliveryDate = DateConverter.fromTimestamp(_tmp_1);
        _item.setDeliveryDate(_tmpDeliveryDate);
        final String _tmpDeliveryTimeSlot;
        if (_cursor.isNull(_cursorIndexOfDeliveryTimeSlot)) {
          _tmpDeliveryTimeSlot = null;
        } else {
          _tmpDeliveryTimeSlot = _cursor.getString(_cursorIndexOfDeliveryTimeSlot);
        }
        _item.setDeliveryTimeSlot(_tmpDeliveryTimeSlot);
        final String _tmpNotes;
        if (_cursor.isNull(_cursorIndexOfNotes)) {
          _tmpNotes = null;
        } else {
          _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
        }
        _item.setNotes(_tmpNotes);
        final Date _tmpCreatedAt;
        final Long _tmp_2;
        if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
          _tmp_2 = null;
        } else {
          _tmp_2 = _cursor.getLong(_cursorIndexOfCreatedAt);
        }
        _tmpCreatedAt = DateConverter.fromTimestamp(_tmp_2);
        _item.setCreatedAt(_tmpCreatedAt);
        final Date _tmpUpdatedAt;
        final Long _tmp_3;
        if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
          _tmp_3 = null;
        } else {
          _tmp_3 = _cursor.getLong(_cursorIndexOfUpdatedAt);
        }
        _tmpUpdatedAt = DateConverter.fromTimestamp(_tmp_3);
        _item.setUpdatedAt(_tmpUpdatedAt);
        final String _tmpCustomerName;
        if (_cursor.isNull(_cursorIndexOfCustomerName)) {
          _tmpCustomerName = null;
        } else {
          _tmpCustomerName = _cursor.getString(_cursorIndexOfCustomerName);
        }
        _item.setCustomerName(_tmpCustomerName);
        final String _tmpCustomerPhone;
        if (_cursor.isNull(_cursorIndexOfCustomerPhone)) {
          _tmpCustomerPhone = null;
        } else {
          _tmpCustomerPhone = _cursor.getString(_cursorIndexOfCustomerPhone);
        }
        _item.setCustomerPhone(_tmpCustomerPhone);
        final String _tmpDeliveryPersonName;
        if (_cursor.isNull(_cursorIndexOfDeliveryPersonName)) {
          _tmpDeliveryPersonName = null;
        } else {
          _tmpDeliveryPersonName = _cursor.getString(_cursorIndexOfDeliveryPersonName);
        }
        _item.setDeliveryPersonName(_tmpDeliveryPersonName);
        final String _tmpDeliveryPersonPhone;
        if (_cursor.isNull(_cursorIndexOfDeliveryPersonPhone)) {
          _tmpDeliveryPersonPhone = null;
        } else {
          _tmpDeliveryPersonPhone = _cursor.getString(_cursorIndexOfDeliveryPersonPhone);
        }
        _item.setDeliveryPersonPhone(_tmpDeliveryPersonPhone);
        final String _tmpPromoCode;
        if (_cursor.isNull(_cursorIndexOfPromoCode)) {
          _tmpPromoCode = null;
        } else {
          _tmpPromoCode = _cursor.getString(_cursorIndexOfPromoCode);
        }
        _item.setPromoCode(_tmpPromoCode);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public LiveData<List<OrderEntity>> getOrdersByUserIdLive(final int userId) {
    final String _sql = "SELECT * FROM orders WHERE user_id = ? ORDER BY created_at DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, userId);
    return __db.getInvalidationTracker().createLiveData(new String[] {"orders"}, false, new Callable<List<OrderEntity>>() {
      @Override
      @Nullable
      public List<OrderEntity> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfOrderNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "order_number");
          final int _cursorIndexOfTrackingNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "tracking_number");
          final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "user_id");
          final int _cursorIndexOfDeliveryPersonnelId = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_personnel_id");
          final int _cursorIndexOfPromoCodeId = CursorUtil.getColumnIndexOrThrow(_cursor, "promo_code_id");
          final int _cursorIndexOfSubtotal = CursorUtil.getColumnIndexOrThrow(_cursor, "subtotal");
          final int _cursorIndexOfDiscount = CursorUtil.getColumnIndexOrThrow(_cursor, "discount");
          final int _cursorIndexOfDeliveryFee = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_fee");
          final int _cursorIndexOfTotal = CursorUtil.getColumnIndexOrThrow(_cursor, "total");
          final int _cursorIndexOfPaymentMethod = CursorUtil.getColumnIndexOrThrow(_cursor, "payment_method");
          final int _cursorIndexOfPaymentStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "payment_status");
          final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
          final int _cursorIndexOfPickupAddress = CursorUtil.getColumnIndexOrThrow(_cursor, "pickup_address");
          final int _cursorIndexOfPickupDivisionId = CursorUtil.getColumnIndexOrThrow(_cursor, "pickup_division_id");
          final int _cursorIndexOfPickupDistrictId = CursorUtil.getColumnIndexOrThrow(_cursor, "pickup_district_id");
          final int _cursorIndexOfPickupUpazillaId = CursorUtil.getColumnIndexOrThrow(_cursor, "pickup_upazilla_id");
          final int _cursorIndexOfPickupDate = CursorUtil.getColumnIndexOrThrow(_cursor, "pickup_date");
          final int _cursorIndexOfPickupTimeSlot = CursorUtil.getColumnIndexOrThrow(_cursor, "pickup_time_slot");
          final int _cursorIndexOfDeliveryAddress = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_address");
          final int _cursorIndexOfDeliveryDivisionId = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_division_id");
          final int _cursorIndexOfDeliveryDistrictId = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_district_id");
          final int _cursorIndexOfDeliveryUpazillaId = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_upazilla_id");
          final int _cursorIndexOfDeliveryDate = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_date");
          final int _cursorIndexOfDeliveryTimeSlot = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_time_slot");
          final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
          final int _cursorIndexOfCustomerName = CursorUtil.getColumnIndexOrThrow(_cursor, "customerName");
          final int _cursorIndexOfCustomerPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "customerPhone");
          final int _cursorIndexOfDeliveryPersonName = CursorUtil.getColumnIndexOrThrow(_cursor, "deliveryPersonName");
          final int _cursorIndexOfDeliveryPersonPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "deliveryPersonPhone");
          final int _cursorIndexOfPromoCode = CursorUtil.getColumnIndexOrThrow(_cursor, "promoCode");
          final List<OrderEntity> _result = new ArrayList<OrderEntity>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final OrderEntity _item;
            _item = new OrderEntity();
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            _item.setId(_tmpId);
            final String _tmpOrderNumber;
            if (_cursor.isNull(_cursorIndexOfOrderNumber)) {
              _tmpOrderNumber = null;
            } else {
              _tmpOrderNumber = _cursor.getString(_cursorIndexOfOrderNumber);
            }
            _item.setOrderNumber(_tmpOrderNumber);
            final String _tmpTrackingNumber;
            if (_cursor.isNull(_cursorIndexOfTrackingNumber)) {
              _tmpTrackingNumber = null;
            } else {
              _tmpTrackingNumber = _cursor.getString(_cursorIndexOfTrackingNumber);
            }
            _item.setTrackingNumber(_tmpTrackingNumber);
            final int _tmpUserId;
            _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
            _item.setUserId(_tmpUserId);
            final Integer _tmpDeliveryPersonnelId;
            if (_cursor.isNull(_cursorIndexOfDeliveryPersonnelId)) {
              _tmpDeliveryPersonnelId = null;
            } else {
              _tmpDeliveryPersonnelId = _cursor.getInt(_cursorIndexOfDeliveryPersonnelId);
            }
            _item.setDeliveryPersonnelId(_tmpDeliveryPersonnelId);
            final Integer _tmpPromoCodeId;
            if (_cursor.isNull(_cursorIndexOfPromoCodeId)) {
              _tmpPromoCodeId = null;
            } else {
              _tmpPromoCodeId = _cursor.getInt(_cursorIndexOfPromoCodeId);
            }
            _item.setPromoCodeId(_tmpPromoCodeId);
            final double _tmpSubtotal;
            _tmpSubtotal = _cursor.getDouble(_cursorIndexOfSubtotal);
            _item.setSubtotal(_tmpSubtotal);
            final double _tmpDiscount;
            _tmpDiscount = _cursor.getDouble(_cursorIndexOfDiscount);
            _item.setDiscount(_tmpDiscount);
            final double _tmpDeliveryFee;
            _tmpDeliveryFee = _cursor.getDouble(_cursorIndexOfDeliveryFee);
            _item.setDeliveryFee(_tmpDeliveryFee);
            final double _tmpTotal;
            _tmpTotal = _cursor.getDouble(_cursorIndexOfTotal);
            _item.setTotal(_tmpTotal);
            final String _tmpPaymentMethod;
            if (_cursor.isNull(_cursorIndexOfPaymentMethod)) {
              _tmpPaymentMethod = null;
            } else {
              _tmpPaymentMethod = _cursor.getString(_cursorIndexOfPaymentMethod);
            }
            _item.setPaymentMethod(_tmpPaymentMethod);
            final String _tmpPaymentStatus;
            if (_cursor.isNull(_cursorIndexOfPaymentStatus)) {
              _tmpPaymentStatus = null;
            } else {
              _tmpPaymentStatus = _cursor.getString(_cursorIndexOfPaymentStatus);
            }
            _item.setPaymentStatus(_tmpPaymentStatus);
            final String _tmpStatus;
            if (_cursor.isNull(_cursorIndexOfStatus)) {
              _tmpStatus = null;
            } else {
              _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
            }
            _item.setStatus(_tmpStatus);
            final String _tmpPickupAddress;
            if (_cursor.isNull(_cursorIndexOfPickupAddress)) {
              _tmpPickupAddress = null;
            } else {
              _tmpPickupAddress = _cursor.getString(_cursorIndexOfPickupAddress);
            }
            _item.setPickupAddress(_tmpPickupAddress);
            final Integer _tmpPickupDivisionId;
            if (_cursor.isNull(_cursorIndexOfPickupDivisionId)) {
              _tmpPickupDivisionId = null;
            } else {
              _tmpPickupDivisionId = _cursor.getInt(_cursorIndexOfPickupDivisionId);
            }
            _item.setPickupDivisionId(_tmpPickupDivisionId);
            final Integer _tmpPickupDistrictId;
            if (_cursor.isNull(_cursorIndexOfPickupDistrictId)) {
              _tmpPickupDistrictId = null;
            } else {
              _tmpPickupDistrictId = _cursor.getInt(_cursorIndexOfPickupDistrictId);
            }
            _item.setPickupDistrictId(_tmpPickupDistrictId);
            final Integer _tmpPickupUpazillaId;
            if (_cursor.isNull(_cursorIndexOfPickupUpazillaId)) {
              _tmpPickupUpazillaId = null;
            } else {
              _tmpPickupUpazillaId = _cursor.getInt(_cursorIndexOfPickupUpazillaId);
            }
            _item.setPickupUpazillaId(_tmpPickupUpazillaId);
            final Date _tmpPickupDate;
            final Long _tmp;
            if (_cursor.isNull(_cursorIndexOfPickupDate)) {
              _tmp = null;
            } else {
              _tmp = _cursor.getLong(_cursorIndexOfPickupDate);
            }
            _tmpPickupDate = DateConverter.fromTimestamp(_tmp);
            _item.setPickupDate(_tmpPickupDate);
            final String _tmpPickupTimeSlot;
            if (_cursor.isNull(_cursorIndexOfPickupTimeSlot)) {
              _tmpPickupTimeSlot = null;
            } else {
              _tmpPickupTimeSlot = _cursor.getString(_cursorIndexOfPickupTimeSlot);
            }
            _item.setPickupTimeSlot(_tmpPickupTimeSlot);
            final String _tmpDeliveryAddress;
            if (_cursor.isNull(_cursorIndexOfDeliveryAddress)) {
              _tmpDeliveryAddress = null;
            } else {
              _tmpDeliveryAddress = _cursor.getString(_cursorIndexOfDeliveryAddress);
            }
            _item.setDeliveryAddress(_tmpDeliveryAddress);
            final Integer _tmpDeliveryDivisionId;
            if (_cursor.isNull(_cursorIndexOfDeliveryDivisionId)) {
              _tmpDeliveryDivisionId = null;
            } else {
              _tmpDeliveryDivisionId = _cursor.getInt(_cursorIndexOfDeliveryDivisionId);
            }
            _item.setDeliveryDivisionId(_tmpDeliveryDivisionId);
            final Integer _tmpDeliveryDistrictId;
            if (_cursor.isNull(_cursorIndexOfDeliveryDistrictId)) {
              _tmpDeliveryDistrictId = null;
            } else {
              _tmpDeliveryDistrictId = _cursor.getInt(_cursorIndexOfDeliveryDistrictId);
            }
            _item.setDeliveryDistrictId(_tmpDeliveryDistrictId);
            final Integer _tmpDeliveryUpazillaId;
            if (_cursor.isNull(_cursorIndexOfDeliveryUpazillaId)) {
              _tmpDeliveryUpazillaId = null;
            } else {
              _tmpDeliveryUpazillaId = _cursor.getInt(_cursorIndexOfDeliveryUpazillaId);
            }
            _item.setDeliveryUpazillaId(_tmpDeliveryUpazillaId);
            final Date _tmpDeliveryDate;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfDeliveryDate)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfDeliveryDate);
            }
            _tmpDeliveryDate = DateConverter.fromTimestamp(_tmp_1);
            _item.setDeliveryDate(_tmpDeliveryDate);
            final String _tmpDeliveryTimeSlot;
            if (_cursor.isNull(_cursorIndexOfDeliveryTimeSlot)) {
              _tmpDeliveryTimeSlot = null;
            } else {
              _tmpDeliveryTimeSlot = _cursor.getString(_cursorIndexOfDeliveryTimeSlot);
            }
            _item.setDeliveryTimeSlot(_tmpDeliveryTimeSlot);
            final String _tmpNotes;
            if (_cursor.isNull(_cursorIndexOfNotes)) {
              _tmpNotes = null;
            } else {
              _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
            }
            _item.setNotes(_tmpNotes);
            final Date _tmpCreatedAt;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            _tmpCreatedAt = DateConverter.fromTimestamp(_tmp_2);
            _item.setCreatedAt(_tmpCreatedAt);
            final Date _tmpUpdatedAt;
            final Long _tmp_3;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_3 = null;
            } else {
              _tmp_3 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            _tmpUpdatedAt = DateConverter.fromTimestamp(_tmp_3);
            _item.setUpdatedAt(_tmpUpdatedAt);
            final String _tmpCustomerName;
            if (_cursor.isNull(_cursorIndexOfCustomerName)) {
              _tmpCustomerName = null;
            } else {
              _tmpCustomerName = _cursor.getString(_cursorIndexOfCustomerName);
            }
            _item.setCustomerName(_tmpCustomerName);
            final String _tmpCustomerPhone;
            if (_cursor.isNull(_cursorIndexOfCustomerPhone)) {
              _tmpCustomerPhone = null;
            } else {
              _tmpCustomerPhone = _cursor.getString(_cursorIndexOfCustomerPhone);
            }
            _item.setCustomerPhone(_tmpCustomerPhone);
            final String _tmpDeliveryPersonName;
            if (_cursor.isNull(_cursorIndexOfDeliveryPersonName)) {
              _tmpDeliveryPersonName = null;
            } else {
              _tmpDeliveryPersonName = _cursor.getString(_cursorIndexOfDeliveryPersonName);
            }
            _item.setDeliveryPersonName(_tmpDeliveryPersonName);
            final String _tmpDeliveryPersonPhone;
            if (_cursor.isNull(_cursorIndexOfDeliveryPersonPhone)) {
              _tmpDeliveryPersonPhone = null;
            } else {
              _tmpDeliveryPersonPhone = _cursor.getString(_cursorIndexOfDeliveryPersonPhone);
            }
            _item.setDeliveryPersonPhone(_tmpDeliveryPersonPhone);
            final String _tmpPromoCode;
            if (_cursor.isNull(_cursorIndexOfPromoCode)) {
              _tmpPromoCode = null;
            } else {
              _tmpPromoCode = _cursor.getString(_cursorIndexOfPromoCode);
            }
            _item.setPromoCode(_tmpPromoCode);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public List<OrderEntity> getAllOrders() {
    final String _sql = "SELECT * FROM orders ORDER BY created_at DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfOrderNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "order_number");
      final int _cursorIndexOfTrackingNumber = CursorUtil.getColumnIndexOrThrow(_cursor, "tracking_number");
      final int _cursorIndexOfUserId = CursorUtil.getColumnIndexOrThrow(_cursor, "user_id");
      final int _cursorIndexOfDeliveryPersonnelId = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_personnel_id");
      final int _cursorIndexOfPromoCodeId = CursorUtil.getColumnIndexOrThrow(_cursor, "promo_code_id");
      final int _cursorIndexOfSubtotal = CursorUtil.getColumnIndexOrThrow(_cursor, "subtotal");
      final int _cursorIndexOfDiscount = CursorUtil.getColumnIndexOrThrow(_cursor, "discount");
      final int _cursorIndexOfDeliveryFee = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_fee");
      final int _cursorIndexOfTotal = CursorUtil.getColumnIndexOrThrow(_cursor, "total");
      final int _cursorIndexOfPaymentMethod = CursorUtil.getColumnIndexOrThrow(_cursor, "payment_method");
      final int _cursorIndexOfPaymentStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "payment_status");
      final int _cursorIndexOfStatus = CursorUtil.getColumnIndexOrThrow(_cursor, "status");
      final int _cursorIndexOfPickupAddress = CursorUtil.getColumnIndexOrThrow(_cursor, "pickup_address");
      final int _cursorIndexOfPickupDivisionId = CursorUtil.getColumnIndexOrThrow(_cursor, "pickup_division_id");
      final int _cursorIndexOfPickupDistrictId = CursorUtil.getColumnIndexOrThrow(_cursor, "pickup_district_id");
      final int _cursorIndexOfPickupUpazillaId = CursorUtil.getColumnIndexOrThrow(_cursor, "pickup_upazilla_id");
      final int _cursorIndexOfPickupDate = CursorUtil.getColumnIndexOrThrow(_cursor, "pickup_date");
      final int _cursorIndexOfPickupTimeSlot = CursorUtil.getColumnIndexOrThrow(_cursor, "pickup_time_slot");
      final int _cursorIndexOfDeliveryAddress = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_address");
      final int _cursorIndexOfDeliveryDivisionId = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_division_id");
      final int _cursorIndexOfDeliveryDistrictId = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_district_id");
      final int _cursorIndexOfDeliveryUpazillaId = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_upazilla_id");
      final int _cursorIndexOfDeliveryDate = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_date");
      final int _cursorIndexOfDeliveryTimeSlot = CursorUtil.getColumnIndexOrThrow(_cursor, "delivery_time_slot");
      final int _cursorIndexOfNotes = CursorUtil.getColumnIndexOrThrow(_cursor, "notes");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "created_at");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updated_at");
      final int _cursorIndexOfCustomerName = CursorUtil.getColumnIndexOrThrow(_cursor, "customerName");
      final int _cursorIndexOfCustomerPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "customerPhone");
      final int _cursorIndexOfDeliveryPersonName = CursorUtil.getColumnIndexOrThrow(_cursor, "deliveryPersonName");
      final int _cursorIndexOfDeliveryPersonPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "deliveryPersonPhone");
      final int _cursorIndexOfPromoCode = CursorUtil.getColumnIndexOrThrow(_cursor, "promoCode");
      final List<OrderEntity> _result = new ArrayList<OrderEntity>(_cursor.getCount());
      while (_cursor.moveToNext()) {
        final OrderEntity _item;
        _item = new OrderEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _item.setId(_tmpId);
        final String _tmpOrderNumber;
        if (_cursor.isNull(_cursorIndexOfOrderNumber)) {
          _tmpOrderNumber = null;
        } else {
          _tmpOrderNumber = _cursor.getString(_cursorIndexOfOrderNumber);
        }
        _item.setOrderNumber(_tmpOrderNumber);
        final String _tmpTrackingNumber;
        if (_cursor.isNull(_cursorIndexOfTrackingNumber)) {
          _tmpTrackingNumber = null;
        } else {
          _tmpTrackingNumber = _cursor.getString(_cursorIndexOfTrackingNumber);
        }
        _item.setTrackingNumber(_tmpTrackingNumber);
        final int _tmpUserId;
        _tmpUserId = _cursor.getInt(_cursorIndexOfUserId);
        _item.setUserId(_tmpUserId);
        final Integer _tmpDeliveryPersonnelId;
        if (_cursor.isNull(_cursorIndexOfDeliveryPersonnelId)) {
          _tmpDeliveryPersonnelId = null;
        } else {
          _tmpDeliveryPersonnelId = _cursor.getInt(_cursorIndexOfDeliveryPersonnelId);
        }
        _item.setDeliveryPersonnelId(_tmpDeliveryPersonnelId);
        final Integer _tmpPromoCodeId;
        if (_cursor.isNull(_cursorIndexOfPromoCodeId)) {
          _tmpPromoCodeId = null;
        } else {
          _tmpPromoCodeId = _cursor.getInt(_cursorIndexOfPromoCodeId);
        }
        _item.setPromoCodeId(_tmpPromoCodeId);
        final double _tmpSubtotal;
        _tmpSubtotal = _cursor.getDouble(_cursorIndexOfSubtotal);
        _item.setSubtotal(_tmpSubtotal);
        final double _tmpDiscount;
        _tmpDiscount = _cursor.getDouble(_cursorIndexOfDiscount);
        _item.setDiscount(_tmpDiscount);
        final double _tmpDeliveryFee;
        _tmpDeliveryFee = _cursor.getDouble(_cursorIndexOfDeliveryFee);
        _item.setDeliveryFee(_tmpDeliveryFee);
        final double _tmpTotal;
        _tmpTotal = _cursor.getDouble(_cursorIndexOfTotal);
        _item.setTotal(_tmpTotal);
        final String _tmpPaymentMethod;
        if (_cursor.isNull(_cursorIndexOfPaymentMethod)) {
          _tmpPaymentMethod = null;
        } else {
          _tmpPaymentMethod = _cursor.getString(_cursorIndexOfPaymentMethod);
        }
        _item.setPaymentMethod(_tmpPaymentMethod);
        final String _tmpPaymentStatus;
        if (_cursor.isNull(_cursorIndexOfPaymentStatus)) {
          _tmpPaymentStatus = null;
        } else {
          _tmpPaymentStatus = _cursor.getString(_cursorIndexOfPaymentStatus);
        }
        _item.setPaymentStatus(_tmpPaymentStatus);
        final String _tmpStatus;
        if (_cursor.isNull(_cursorIndexOfStatus)) {
          _tmpStatus = null;
        } else {
          _tmpStatus = _cursor.getString(_cursorIndexOfStatus);
        }
        _item.setStatus(_tmpStatus);
        final String _tmpPickupAddress;
        if (_cursor.isNull(_cursorIndexOfPickupAddress)) {
          _tmpPickupAddress = null;
        } else {
          _tmpPickupAddress = _cursor.getString(_cursorIndexOfPickupAddress);
        }
        _item.setPickupAddress(_tmpPickupAddress);
        final Integer _tmpPickupDivisionId;
        if (_cursor.isNull(_cursorIndexOfPickupDivisionId)) {
          _tmpPickupDivisionId = null;
        } else {
          _tmpPickupDivisionId = _cursor.getInt(_cursorIndexOfPickupDivisionId);
        }
        _item.setPickupDivisionId(_tmpPickupDivisionId);
        final Integer _tmpPickupDistrictId;
        if (_cursor.isNull(_cursorIndexOfPickupDistrictId)) {
          _tmpPickupDistrictId = null;
        } else {
          _tmpPickupDistrictId = _cursor.getInt(_cursorIndexOfPickupDistrictId);
        }
        _item.setPickupDistrictId(_tmpPickupDistrictId);
        final Integer _tmpPickupUpazillaId;
        if (_cursor.isNull(_cursorIndexOfPickupUpazillaId)) {
          _tmpPickupUpazillaId = null;
        } else {
          _tmpPickupUpazillaId = _cursor.getInt(_cursorIndexOfPickupUpazillaId);
        }
        _item.setPickupUpazillaId(_tmpPickupUpazillaId);
        final Date _tmpPickupDate;
        final Long _tmp;
        if (_cursor.isNull(_cursorIndexOfPickupDate)) {
          _tmp = null;
        } else {
          _tmp = _cursor.getLong(_cursorIndexOfPickupDate);
        }
        _tmpPickupDate = DateConverter.fromTimestamp(_tmp);
        _item.setPickupDate(_tmpPickupDate);
        final String _tmpPickupTimeSlot;
        if (_cursor.isNull(_cursorIndexOfPickupTimeSlot)) {
          _tmpPickupTimeSlot = null;
        } else {
          _tmpPickupTimeSlot = _cursor.getString(_cursorIndexOfPickupTimeSlot);
        }
        _item.setPickupTimeSlot(_tmpPickupTimeSlot);
        final String _tmpDeliveryAddress;
        if (_cursor.isNull(_cursorIndexOfDeliveryAddress)) {
          _tmpDeliveryAddress = null;
        } else {
          _tmpDeliveryAddress = _cursor.getString(_cursorIndexOfDeliveryAddress);
        }
        _item.setDeliveryAddress(_tmpDeliveryAddress);
        final Integer _tmpDeliveryDivisionId;
        if (_cursor.isNull(_cursorIndexOfDeliveryDivisionId)) {
          _tmpDeliveryDivisionId = null;
        } else {
          _tmpDeliveryDivisionId = _cursor.getInt(_cursorIndexOfDeliveryDivisionId);
        }
        _item.setDeliveryDivisionId(_tmpDeliveryDivisionId);
        final Integer _tmpDeliveryDistrictId;
        if (_cursor.isNull(_cursorIndexOfDeliveryDistrictId)) {
          _tmpDeliveryDistrictId = null;
        } else {
          _tmpDeliveryDistrictId = _cursor.getInt(_cursorIndexOfDeliveryDistrictId);
        }
        _item.setDeliveryDistrictId(_tmpDeliveryDistrictId);
        final Integer _tmpDeliveryUpazillaId;
        if (_cursor.isNull(_cursorIndexOfDeliveryUpazillaId)) {
          _tmpDeliveryUpazillaId = null;
        } else {
          _tmpDeliveryUpazillaId = _cursor.getInt(_cursorIndexOfDeliveryUpazillaId);
        }
        _item.setDeliveryUpazillaId(_tmpDeliveryUpazillaId);
        final Date _tmpDeliveryDate;
        final Long _tmp_1;
        if (_cursor.isNull(_cursorIndexOfDeliveryDate)) {
          _tmp_1 = null;
        } else {
          _tmp_1 = _cursor.getLong(_cursorIndexOfDeliveryDate);
        }
        _tmpDeliveryDate = DateConverter.fromTimestamp(_tmp_1);
        _item.setDeliveryDate(_tmpDeliveryDate);
        final String _tmpDeliveryTimeSlot;
        if (_cursor.isNull(_cursorIndexOfDeliveryTimeSlot)) {
          _tmpDeliveryTimeSlot = null;
        } else {
          _tmpDeliveryTimeSlot = _cursor.getString(_cursorIndexOfDeliveryTimeSlot);
        }
        _item.setDeliveryTimeSlot(_tmpDeliveryTimeSlot);
        final String _tmpNotes;
        if (_cursor.isNull(_cursorIndexOfNotes)) {
          _tmpNotes = null;
        } else {
          _tmpNotes = _cursor.getString(_cursorIndexOfNotes);
        }
        _item.setNotes(_tmpNotes);
        final Date _tmpCreatedAt;
        final Long _tmp_2;
        if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
          _tmp_2 = null;
        } else {
          _tmp_2 = _cursor.getLong(_cursorIndexOfCreatedAt);
        }
        _tmpCreatedAt = DateConverter.fromTimestamp(_tmp_2);
        _item.setCreatedAt(_tmpCreatedAt);
        final Date _tmpUpdatedAt;
        final Long _tmp_3;
        if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
          _tmp_3 = null;
        } else {
          _tmp_3 = _cursor.getLong(_cursorIndexOfUpdatedAt);
        }
        _tmpUpdatedAt = DateConverter.fromTimestamp(_tmp_3);
        _item.setUpdatedAt(_tmpUpdatedAt);
        final String _tmpCustomerName;
        if (_cursor.isNull(_cursorIndexOfCustomerName)) {
          _tmpCustomerName = null;
        } else {
          _tmpCustomerName = _cursor.getString(_cursorIndexOfCustomerName);
        }
        _item.setCustomerName(_tmpCustomerName);
        final String _tmpCustomerPhone;
        if (_cursor.isNull(_cursorIndexOfCustomerPhone)) {
          _tmpCustomerPhone = null;
        } else {
          _tmpCustomerPhone = _cursor.getString(_cursorIndexOfCustomerPhone);
        }
        _item.setCustomerPhone(_tmpCustomerPhone);
        final String _tmpDeliveryPersonName;
        if (_cursor.isNull(_cursorIndexOfDeliveryPersonName)) {
          _tmpDeliveryPersonName = null;
        } else {
          _tmpDeliveryPersonName = _cursor.getString(_cursorIndexOfDeliveryPersonName);
        }
        _item.setDeliveryPersonName(_tmpDeliveryPersonName);
        final String _tmpDeliveryPersonPhone;
        if (_cursor.isNull(_cursorIndexOfDeliveryPersonPhone)) {
          _tmpDeliveryPersonPhone = null;
        } else {
          _tmpDeliveryPersonPhone = _cursor.getString(_cursorIndexOfDeliveryPersonPhone);
        }
        _item.setDeliveryPersonPhone(_tmpDeliveryPersonPhone);
        final String _tmpPromoCode;
        if (_cursor.isNull(_cursorIndexOfPromoCode)) {
          _tmpPromoCode = null;
        } else {
          _tmpPromoCode = _cursor.getString(_cursorIndexOfPromoCode);
        }
        _item.setPromoCode(_tmpPromoCode);
        _result.add(_item);
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
