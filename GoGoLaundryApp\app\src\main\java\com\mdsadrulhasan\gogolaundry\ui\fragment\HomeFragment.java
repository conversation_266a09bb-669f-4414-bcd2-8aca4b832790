package com.mdsadrulhasan.gogolaundry.ui.fragment;

import android.app.AlertDialog;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.facebook.shimmer.ShimmerFrameLayout;

import android.os.Handler;
import android.os.Looper;

import com.google.android.material.button.MaterialButton;
import com.google.android.material.card.MaterialCardView;
import com.mdsadrulhasan.gogolaundry.MainActivity;
import com.mdsadrulhasan.gogolaundry.R;
import com.mdsadrulhasan.gogolaundry.adapter.PopularItemAdapter;
import com.mdsadrulhasan.gogolaundry.adapter.PopularServiceAdapter;
import com.mdsadrulhasan.gogolaundry.adapter.RecentOrderAdapter;
import com.mdsadrulhasan.gogolaundry.adapter.ServiceAdapter;
import com.mdsadrulhasan.gogolaundry.adapter.ServiceGridAdapter;
import com.mdsadrulhasan.gogolaundry.database.entity.ItemEntity;
import com.mdsadrulhasan.gogolaundry.database.entity.OrderEntity;
import com.mdsadrulhasan.gogolaundry.database.entity.ServiceEntity;
import com.mdsadrulhasan.gogolaundry.model.Item;
import com.mdsadrulhasan.gogolaundry.model.Order;
import com.mdsadrulhasan.gogolaundry.model.OrderItem;
import com.mdsadrulhasan.gogolaundry.model.Service;
import com.mdsadrulhasan.gogolaundry.model.User;
import com.mdsadrulhasan.gogolaundry.ui.fragment.InvoiceFragment;
import com.mdsadrulhasan.gogolaundry.utils.Resource;
import com.mdsadrulhasan.gogolaundry.utils.SessionManager;
import com.mdsadrulhasan.gogolaundry.utils.ToastUtils;
import com.mdsadrulhasan.gogolaundry.viewmodel.CartViewModel;
import com.mdsadrulhasan.gogolaundry.viewmodel.ItemsViewModel;
import com.mdsadrulhasan.gogolaundry.viewmodel.OrdersViewModel;
import com.mdsadrulhasan.gogolaundry.viewmodel.ServicesViewModel;
import com.mdsadrulhasan.gogolaundry.ui.dialog.PromoDialog;

import java.util.ArrayList;
import java.util.List;

/**
 * Home fragment that displays welcome message, order summary, popular services, and popular items
 */
public class HomeFragment extends Fragment {

    // Shimmer timing constants
    private static final long SHIMMER_DURATION = 2000; // 2 seconds

    // Timing control variables
    private long recentOrdersShimmerStartTime = 0;
    private long servicesShimmerStartTime = 0;
    private long popularItemsShimmerStartTime = 0;

    // Data loading flags
    private boolean recentOrdersDataLoaded = false;
    private boolean servicesDataLoaded = false;
    private boolean popularItemsDataLoaded = false;

    // Handler for delayed operations
    private Handler mainHandler = new Handler(Looper.getMainLooper());

    // Flag to prevent multiple promo dialog scheduling
    private static boolean promoDialogScheduled = false;
    private static Runnable scheduledPromoTask = null;
    private static Handler staticHandler = new Handler(Looper.getMainLooper());

    // Views for welcome section
    private TextView welcomeText;
    private TextView welcomeSubtitle;





    // Views for services section
    private RecyclerView servicesGrid;
    private ShimmerFrameLayout servicesShimmer;
    private View servicesContainer;
    private MaterialButton viewAllServicesButton;

    // Views for recent orders section
    private RecyclerView recentOrdersRecyclerView;
    private TextView viewAllOrdersText;
    private ShimmerFrameLayout recentOrdersShimmer;
    private View recentOrdersContainer;
    private View recentOrdersEmptyState;
    private MaterialButton btnStartOrder;

    // Views for popular items section
    private RecyclerView popularItemsRecyclerView;
    private ShimmerFrameLayout popularItemsShimmer;
    private View popularItemsContainer;
    private TextView viewAllItemsText;

    // ViewModels
    private ServicesViewModel servicesViewModel;
    private OrdersViewModel ordersViewModel;
    private ItemsViewModel itemsViewModel;
    private CartViewModel cartViewModel;

    // Adapters
    private ServiceGridAdapter serviceGridAdapter;
    private PopularServiceAdapter popularServiceAdapter;
    private RecentOrderAdapter recentOrderAdapter;
    private PopularItemAdapter popularItemAdapter;

    // Session manager
    private SessionManager sessionManager;
    private User currentUser;

    // Latest order
    private OrderEntity latestOrder;

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        servicesViewModel = new ViewModelProvider(this).get(ServicesViewModel.class);
        ordersViewModel = new ViewModelProvider(this).get(OrdersViewModel.class);
        itemsViewModel = new ViewModelProvider(this).get(ItemsViewModel.class);
        cartViewModel = new ViewModelProvider(requireActivity()).get(CartViewModel.class);
        sessionManager = new SessionManager(requireContext());
        currentUser = sessionManager.getUser();
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_home, container, false);

        // Initialize views
        initViews(view);

        // Set up welcome section
        setupWelcomeSection();

        // Set up services grid
        setupServicesGrid();

        // Set up recent orders recycler view
        setupRecentOrdersRecyclerView();

        // Set up popular items recycler view
        setupPopularItemsRecyclerView();

        // Load data
        loadData();

        // Set up click listeners
        setupClickListeners();

        // Show promotional dialog on every app launch
        // DISABLED: Now handled by MainActivity
        // showPromoDialogOnAppLaunch();

        return view;
    }

    /**
     * Initialize all views
     */
    private void initViews(View view) {
        // Welcome section
        welcomeText = view.findViewById(R.id.welcome_text);
        welcomeSubtitle = view.findViewById(R.id.welcome_subtitle);





        // Services grid
        servicesContainer = view.findViewById(R.id.services_container);
        servicesGrid = view.findViewById(R.id.services_grid);
        servicesShimmer = view.findViewById(R.id.services_shimmer);
        // Find the "View All" button in services section
        viewAllServicesButton = view.findViewById(R.id.view_all_services);

        // Recent orders recycler view
        recentOrdersContainer = view.findViewById(R.id.recent_orders_container);
        recentOrdersRecyclerView = view.findViewById(R.id.recent_orders_recycler_view);
        recentOrdersShimmer = view.findViewById(R.id.recent_orders_shimmer);
        recentOrdersEmptyState = view.findViewById(R.id.recent_orders_empty_state);
        btnStartOrder = view.findViewById(R.id.btn_start_order);
        viewAllOrdersText = view.findViewById(R.id.view_all_orders);

        // Popular items recycler view
        popularItemsContainer = view.findViewById(R.id.popular_items_container);
        popularItemsRecyclerView = view.findViewById(R.id.popular_items_recycler_view);
        popularItemsShimmer = view.findViewById(R.id.popular_items_shimmer);
        // Find the "View All" text in items header
        LinearLayout itemsHeader = view.findViewById(R.id.items_header);
        if (itemsHeader != null && itemsHeader.getChildCount() > 1) {
            viewAllItemsText = (TextView) itemsHeader.getChildAt(1);
        }


    }

    /**
     * Set up welcome section with user's name
     */
    private void setupWelcomeSection() {
        if (currentUser != null) {
            String firstName = currentUser.getFullName().split(" ")[0];
            welcomeText.setText(getString(R.string.hello_user, firstName));
        }
    }

    /**
     * Set up services grid (now horizontal with enhanced sizing)
     */
    private void setupServicesGrid() {
        LinearLayoutManager layoutManager = new LinearLayoutManager(requireContext(), LinearLayoutManager.HORIZONTAL, false);
        servicesGrid.setLayoutManager(layoutManager);

        // Use PopularServiceAdapter for better sizing and visibility
        popularServiceAdapter = new PopularServiceAdapter(new ArrayList<>(), new PopularServiceAdapter.ServiceClickListener() {
            @Override
            public void onServiceClicked(Service service) {
                // Navigate to items screen for this service
                if (getActivity() != null) {
                    ItemsFragment itemsFragment = ItemsFragment.newInstance(service.getId(), service.getName());
                    getActivity().getSupportFragmentManager().beginTransaction()
                            .replace(R.id.fragment_container, itemsFragment)
                            .addToBackStack(null)
                            .commit();
                }
            }

            @Override
            public void onViewItemsClicked(Service service) {
                // Navigate to items screen for this service
                if (getActivity() != null) {
                    ItemsFragment itemsFragment = ItemsFragment.newInstance(service.getId(), service.getName());
                    getActivity().getSupportFragmentManager().beginTransaction()
                            .replace(R.id.fragment_container, itemsFragment)
                            .addToBackStack(null)
                            .commit();
                }
            }
        });
        servicesGrid.setAdapter(popularServiceAdapter);
    }

    /**
     * Set up recent orders recycler view
     */
    private void setupRecentOrdersRecyclerView() {
        recentOrdersRecyclerView.setLayoutManager(
                new LinearLayoutManager(requireContext(), LinearLayoutManager.HORIZONTAL, false));

        recentOrderAdapter = new RecentOrderAdapter(requireContext(), new ArrayList<>(), new RecentOrderAdapter.OnOrderClickListener() {
            @Override
            public void onOrderClick(OrderEntity order) {
                // Navigate to order details/tracking
                if (getActivity() != null) {
                    OrderTrackingFragment trackingFragment = OrderTrackingFragment.newInstance(
                            order.getTrackingNumber(), order.getOrderNumber());
                    getActivity().getSupportFragmentManager().beginTransaction()
                            .replace(R.id.fragment_container, trackingFragment)
                            .addToBackStack(null)
                            .commit();
                }
            }

            @Override
            public void onTrackOrderClick(OrderEntity order) {
                // Navigate to order tracking
                if (getActivity() != null) {
                    OrderTrackingFragment trackingFragment = OrderTrackingFragment.newInstance(
                            order.getTrackingNumber(), order.getOrderNumber());
                    getActivity().getSupportFragmentManager().beginTransaction()
                            .replace(R.id.fragment_container, trackingFragment)
                            .addToBackStack(null)
                            .commit();
                }
            }

            @Override
            public void onReceiptClick(OrderEntity order) {
                // Navigate to invoice/receipt
                if (getActivity() != null) {
                    InvoiceFragment invoiceFragment = InvoiceFragment.newInstance(
                            order.getId(), order.getOrderNumber(), order.getTrackingNumber());
                    getActivity().getSupportFragmentManager().beginTransaction()
                            .replace(R.id.fragment_container, invoiceFragment)
                            .addToBackStack(null)
                            .commit();
                }
            }

            @Override
            public void onReorderClick(OrderEntity order) {
                // Handle reorder functionality
                if (getActivity() != null) {
                    // Show confirmation dialog
                    new AlertDialog.Builder(requireContext())
                            .setTitle("Reorder")
                            .setMessage("Do you want to reorder the items from order #" + order.getOrderNumber() + "?")
                            .setPositiveButton("Yes", (dialog, which) -> {
                                // TODO: Implement reorder logic
                                // This would typically involve:
                                // 1. Fetching the order items
                                // 2. Adding them to the cart
                                // 3. Navigating to checkout
                                ToastUtils.showSuccessToast(requireContext(), "Items added to cart for reorder");

                                // Navigate to cart
                                CartFragment cartFragment = new CartFragment();
                                getActivity().getSupportFragmentManager().beginTransaction()
                                        .replace(R.id.fragment_container, cartFragment)
                                        .addToBackStack(null)
                                        .commit();
                            })
                            .setNegativeButton("No", null)
                            .show();
                }
            }
        });

        recentOrdersRecyclerView.setAdapter(recentOrderAdapter);

        // Set up "View All Orders" click listener
        viewAllOrdersText.setOnClickListener(v -> {
            navigateToOrders();
        });
    }

    /**
     * Set up popular items recycler view
     */
    private void setupPopularItemsRecyclerView() {
        popularItemsRecyclerView.setLayoutManager(
                new LinearLayoutManager(requireContext(), LinearLayoutManager.HORIZONTAL, false));

        popularItemAdapter = new PopularItemAdapter(new ArrayList<>(), new PopularItemAdapter.ItemClickListener() {
            @Override
            public void onItemClicked(Item item) {
                // Navigate to item details or service items
                if (getActivity() != null) {
                    ItemsFragment itemsFragment = ItemsFragment.newInstance(item.getServiceId(), "Items");
                    getActivity().getSupportFragmentManager().beginTransaction()
                            .replace(R.id.fragment_container, itemsFragment)
                            .addToBackStack(null)
                            .commit();
                }
            }

            @Override
            public void onAddToCartClicked(Item item, int position) {
                // Add item to cart
                cartViewModel.addToCart(item, 1);

                // Show success message
                ToastUtils.showSuccessToast(requireContext(), item.getName() + " added to cart");

                // Optionally navigate to cart
                if (getActivity() != null) {
                    // Uncomment to navigate to cart automatically
                    // CartFragment cartFragment = new CartFragment();
                    // getActivity().getSupportFragmentManager().beginTransaction()
                    //         .replace(R.id.fragment_container, cartFragment)
                    //         .addToBackStack(null)
                    //         .commit();
                }
            }
        });

        popularItemsRecyclerView.setAdapter(popularItemAdapter);
    }



    /**
     * Load all data (services, popular items, orders)
     */
    private void loadData() {
        // Start shimmer effects immediately
        startShimmerEffects();

        // Delay API calls by 6 seconds for better UX
        mainHandler.postDelayed(() -> {
            loadServices();
            loadRecentOrders();
            loadPopularItems();
        }, SHIMMER_DURATION);
    }

    /**
     * Start all shimmer effects
     */
    private void startShimmerEffects() {
        // Record start times
        long currentTime = System.currentTimeMillis();
        recentOrdersShimmerStartTime = currentTime;
        servicesShimmerStartTime = currentTime;
        popularItemsShimmerStartTime = currentTime;

        // Reset data loaded flags
        recentOrdersDataLoaded = false;
        servicesDataLoaded = false;
        popularItemsDataLoaded = false;

        // Start shimmer effects
        showRecentOrdersShimmer();
        showServicesShimmer();
        showPopularItemsShimmer();
    }

    /**
     * Load recent orders for the current user
     */
    private void loadRecentOrders() {
        if (currentUser == null) {
            // Hide recent orders section if no user after minimum shimmer time
            scheduleRecentOrdersHide();
            return;
        }

        // Load recent orders (limit to 5 most recent)
        ordersViewModel.getOrdersByUserId(currentUser.getId(), true).observe(getViewLifecycleOwner(), resource -> {
            if (resource.isSuccess()) {
                List<OrderEntity> orders = resource.getData();
                if (orders != null && !orders.isEmpty()) {
                    // Show only the 5 most recent orders
                    List<OrderEntity> recentOrders = orders.size() > 5 ?
                            orders.subList(0, 5) : orders;
                    recentOrderAdapter.updateOrders(recentOrders);
                    recentOrdersDataLoaded = true;
                    scheduleRecentOrdersShow();
                } else {
                    // No orders found, hide the section
                    recentOrdersDataLoaded = true;
                    scheduleRecentOrdersHide();
                }
            } else if (resource.isError()) {
                // Error loading orders, hide the section
                recentOrdersDataLoaded = true;
                scheduleRecentOrdersHide();
                ToastUtils.showErrorToast(requireContext(), "Error loading recent orders: " + resource.getMessage());
            }
        });
    }

    /**
     * Schedule showing recent orders content after minimum shimmer time
     */
    private void scheduleRecentOrdersShow() {
        long elapsedTime = System.currentTimeMillis() - recentOrdersShimmerStartTime;
        long remainingTime = Math.max(0, SHIMMER_DURATION - elapsedTime);

        mainHandler.postDelayed(() -> {
            if (recentOrdersDataLoaded) {
                showRecentOrdersContent();
            }
        }, remainingTime);
    }

    /**
     * Schedule showing empty state for recent orders after minimum shimmer time
     */
    private void scheduleRecentOrdersHide() {
        long elapsedTime = System.currentTimeMillis() - recentOrdersShimmerStartTime;
        long remainingTime = Math.max(0, SHIMMER_DURATION - elapsedTime);

        mainHandler.postDelayed(() -> {
            showRecentOrdersEmptyState();
        }, remainingTime);
    }

    /**
     * Show shimmer effect for recent orders
     */
    private void showRecentOrdersShimmer() {
        // Ensure container is visible
        if (recentOrdersContainer != null) {
            recentOrdersContainer.setVisibility(View.VISIBLE);
        }

        if (recentOrdersShimmer != null) {
            recentOrdersShimmer.setVisibility(View.VISIBLE);
            recentOrdersShimmer.startShimmer();
        }

        if (recentOrdersRecyclerView != null) {
            recentOrdersRecyclerView.setVisibility(View.GONE);
        }

        if (recentOrdersEmptyState != null) {
            recentOrdersEmptyState.setVisibility(View.GONE);
        }
    }

    /**
     * Show recent orders content and hide shimmer
     */
    private void showRecentOrdersContent() {
        // Ensure container is visible
        if (recentOrdersContainer != null) {
            recentOrdersContainer.setVisibility(View.VISIBLE);
        }

        if (recentOrdersShimmer != null) {
            recentOrdersShimmer.stopShimmer();
            recentOrdersShimmer.setVisibility(View.GONE);
        }

        if (recentOrdersRecyclerView != null) {
            recentOrdersRecyclerView.setVisibility(View.VISIBLE);
        }

        if (recentOrdersEmptyState != null) {
            recentOrdersEmptyState.setVisibility(View.GONE);
        }
    }

    /**
     * Show empty state for recent orders
     */
    private void showRecentOrdersEmptyState() {
        // Ensure container is visible
        if (recentOrdersContainer != null) {
            recentOrdersContainer.setVisibility(View.VISIBLE);
        }

        if (recentOrdersShimmer != null) {
            recentOrdersShimmer.stopShimmer();
            recentOrdersShimmer.setVisibility(View.GONE);
        }

        if (recentOrdersRecyclerView != null) {
            recentOrdersRecyclerView.setVisibility(View.GONE);
        }

        if (recentOrdersEmptyState != null) {
            recentOrdersEmptyState.setVisibility(View.VISIBLE);
        }
    }

    /**
     * Load popular items
     */
    private void loadPopularItems() {
        // Load popular items from all services
        itemsViewModel.getPopularItems().observe(getViewLifecycleOwner(), resource -> {
            if (resource.isSuccess()) {
                List<ItemEntity> itemEntities = resource.getData();
                if (itemEntities != null && !itemEntities.isEmpty()) {
                    // Convert entities to UI models
                    List<Item> items = convertToItemModels(itemEntities);
                    popularItemAdapter.updateItems(items);
                    popularItemsDataLoaded = true;
                    schedulePopularItemsShow();
                } else {
                    popularItemsDataLoaded = true;
                    schedulePopularItemsHide();
                }
            } else if (resource.isError()) {
                // Error loading items
                popularItemsDataLoaded = true;
                schedulePopularItemsHide();
                ToastUtils.showErrorToast(requireContext(), "Error loading popular items: " + resource.getMessage());
            }
        });

        // Refresh items
        itemsViewModel.refreshPopularItems();
    }

    /**
     * Schedule showing popular items content after minimum shimmer time
     */
    private void schedulePopularItemsShow() {
        long elapsedTime = System.currentTimeMillis() - popularItemsShimmerStartTime;
        long remainingTime = Math.max(0, SHIMMER_DURATION - elapsedTime);

        mainHandler.postDelayed(() -> {
            if (popularItemsDataLoaded) {
                showPopularItemsContent();
            }
        }, remainingTime);
    }

    /**
     * Schedule hiding popular items section after minimum shimmer time
     */
    private void schedulePopularItemsHide() {
        long elapsedTime = System.currentTimeMillis() - popularItemsShimmerStartTime;
        long remainingTime = Math.max(0, SHIMMER_DURATION - elapsedTime);

        mainHandler.postDelayed(() -> {
            hidePopularItemsSection();
        }, remainingTime);
    }

    /**
     * Show shimmer effect for popular items
     */
    private void showPopularItemsShimmer() {
        if (popularItemsContainer != null) {
            popularItemsContainer.setVisibility(View.VISIBLE);
        }

        if (popularItemsShimmer != null) {
            popularItemsShimmer.setVisibility(View.VISIBLE);
            popularItemsShimmer.startShimmer();
        }

        if (popularItemsRecyclerView != null) {
            popularItemsRecyclerView.setVisibility(View.GONE);
        }
    }

    /**
     * Show popular items content and hide shimmer
     */
    private void showPopularItemsContent() {
        if (popularItemsContainer != null) {
            popularItemsContainer.setVisibility(View.VISIBLE);
        }

        if (popularItemsShimmer != null) {
            popularItemsShimmer.stopShimmer();
            popularItemsShimmer.setVisibility(View.GONE);
        }

        if (popularItemsRecyclerView != null) {
            popularItemsRecyclerView.setVisibility(View.VISIBLE);
        }
    }

    /**
     * Hide entire popular items section
     */
    private void hidePopularItemsSection() {
        if (popularItemsShimmer != null) {
            popularItemsShimmer.stopShimmer();
            popularItemsShimmer.setVisibility(View.GONE);
        }

        if (popularItemsRecyclerView != null) {
            popularItemsRecyclerView.setVisibility(View.GONE);
        }

        if (popularItemsContainer != null) {
            popularItemsContainer.setVisibility(View.GONE);
        }
    }

    /**
     * Convert ItemEntity list to Item model list
     *
     * @param itemEntities List of ItemEntity from database
     * @return List of Item models for UI
     */
    private List<Item> convertToItemModels(List<ItemEntity> itemEntities) {
        List<Item> items = new ArrayList<>();

        for (ItemEntity entity : itemEntities) {
            Item item = new Item();
            item.setId(entity.getId());
            item.setServiceId(entity.getServiceId());
            item.setName(entity.getName());
            item.setDescription(entity.getDescription());
            item.setPrice(entity.getPrice());
            item.setImageUrl(entity.getImageUrl());
            item.setActive(entity.isActive());
            item.setInStock(entity.isInStock());

            items.add(item);
        }

        return items;
    }

    /**
     * Load services data
     */
    private void loadServices() {
        // Observe services data
        servicesViewModel.getServices().observe(getViewLifecycleOwner(), resource -> {
            if (resource.isSuccess()) {
                List<ServiceEntity> serviceEntities = resource.getData();
                if (serviceEntities != null && !serviceEntities.isEmpty()) {
                    // Convert entities to UI models
                    List<Service> services = convertToServiceModels(serviceEntities);
                    popularServiceAdapter.updateServices(services);
                    servicesDataLoaded = true;
                    scheduleServicesShow();
                } else {
                    servicesDataLoaded = true;
                    scheduleServicesHide();
                }
            } else if (resource.isError()) {
                servicesDataLoaded = true;
                scheduleServicesHide();
            }
        });

        // Load services
        servicesViewModel.refreshServices();
    }

    /**
     * Schedule showing services content after minimum shimmer time
     */
    private void scheduleServicesShow() {
        long elapsedTime = System.currentTimeMillis() - servicesShimmerStartTime;
        long remainingTime = Math.max(0, SHIMMER_DURATION - elapsedTime);

        mainHandler.postDelayed(() -> {
            if (servicesDataLoaded) {
                showServicesContent();
            }
        }, remainingTime);
    }

    /**
     * Schedule hiding services section after minimum shimmer time
     */
    private void scheduleServicesHide() {
        long elapsedTime = System.currentTimeMillis() - servicesShimmerStartTime;
        long remainingTime = Math.max(0, SHIMMER_DURATION - elapsedTime);

        mainHandler.postDelayed(() -> {
            hideServicesSection();
        }, remainingTime);
    }

    /**
     * Show shimmer effect for services
     */
    private void showServicesShimmer() {
        if (servicesContainer != null) {
            servicesContainer.setVisibility(View.VISIBLE);
        }

        if (servicesShimmer != null) {
            servicesShimmer.setVisibility(View.VISIBLE);
            servicesShimmer.startShimmer();
        }

        if (servicesGrid != null) {
            servicesGrid.setVisibility(View.GONE);
        }
    }

    /**
     * Show services content and hide shimmer
     */
    private void showServicesContent() {
        if (servicesContainer != null) {
            servicesContainer.setVisibility(View.VISIBLE);
        }

        if (servicesShimmer != null) {
            servicesShimmer.stopShimmer();
            servicesShimmer.setVisibility(View.GONE);
        }

        if (servicesGrid != null) {
            servicesGrid.setVisibility(View.VISIBLE);
        }
    }

    /**
     * Hide entire services section
     */
    private void hideServicesSection() {
        if (servicesShimmer != null) {
            servicesShimmer.stopShimmer();
            servicesShimmer.setVisibility(View.GONE);
        }

        if (servicesGrid != null) {
            servicesGrid.setVisibility(View.GONE);
        }

        if (servicesContainer != null) {
            servicesContainer.setVisibility(View.GONE);
        }
    }



    /**
     * Convert ServiceEntity list to Service model list
     *
     * @param serviceEntities List of ServiceEntity from database
     * @return List of Service models for UI
     */
    private List<Service> convertToServiceModels(List<ServiceEntity> serviceEntities) {
        List<Service> services = new ArrayList<>();

        for (ServiceEntity entity : serviceEntities) {
            Service service = new Service();
            service.setId(entity.getId());
            service.setName(entity.getName());
            service.setDescription(entity.getDescription());
            service.setIconUrl(entity.getImageUrl());

            // We don't need to set price for services as we're not displaying them
            // Prices should only be shown at the item level

            // Handle active status
            boolean isActive = entity.isActive();
            service.setActive(isActive);
            service.setIsActiveRaw(isActive ? 1 : 0);

            services.add(service);
        }

        return services;
    }



    /**
     * Set up click listeners for all buttons
     */
    private void setupClickListeners() {
        // Set up "Start Order" button click listener
        if (btnStartOrder != null) {
            btnStartOrder.setOnClickListener(v -> {
                navigateToServices();
            });
        }

        // Set up "View All Services" click listener
        if (viewAllServicesButton != null) {
            viewAllServicesButton.setOnClickListener(v -> {
                navigateToServices();
            });
        }

        // Set up "View All Items" click listener
        if (viewAllItemsText != null) {
            viewAllItemsText.setOnClickListener(v -> {
                navigateToPopularItems();
            });
        }
    }

    /**
     * Navigate to Services fragment
     */
    private void navigateToServices() {
        if (getActivity() == null) {
            return;
        }

        try {
            // Create ServicesFragment
            ServicesFragment servicesFragment = new ServicesFragment();

            // Clear back stack to avoid navigation issues
            getActivity().getSupportFragmentManager().popBackStack(null,
                androidx.fragment.app.FragmentManager.POP_BACK_STACK_INCLUSIVE);

            // Replace current fragment with ServicesFragment
            getActivity().getSupportFragmentManager().beginTransaction()
                    .setCustomAnimations(R.anim.fade_in, R.anim.fade_out)
                    .replace(R.id.fragment_container, servicesFragment)
                    .commit();

            // Update bottom navigation if MainActivity has the method
            if (getActivity() instanceof MainActivity) {
                MainActivity mainActivity = (MainActivity) getActivity();

                // Update bottom navigation selection to Services
                mainActivity.updateBottomNavigation(R.id.nav_services);
            }
        } catch (Exception e) {
            // Show error toast as fallback
            if (isAdded() && getContext() != null) {
                ToastUtils.showErrorToast(getContext(), "Error navigating to Services");
            }
        }
    }

    /**
     * Navigate to Orders fragment
     */
    private void navigateToOrders() {
        if (getActivity() == null) {
            return;
        }

        try {
            // Create OrdersFragment
            OrdersFragment ordersFragment = new OrdersFragment();

            // Clear back stack to avoid navigation issues
            getActivity().getSupportFragmentManager().popBackStack(null,
                androidx.fragment.app.FragmentManager.POP_BACK_STACK_INCLUSIVE);

            // Replace current fragment with OrdersFragment
            getActivity().getSupportFragmentManager().beginTransaction()
                    .setCustomAnimations(R.anim.fade_in, R.anim.fade_out)
                    .replace(R.id.fragment_container, ordersFragment)
                    .commit();

            // Orders is now accessed via drawer menu, no bottom nav update needed
        } catch (Exception e) {
            // Show error toast as fallback
            if (isAdded() && getContext() != null) {
                ToastUtils.showErrorToast(getContext(), "Error navigating to Orders");
            }
        }
    }

    /**
     * Navigate to Popular Services fragment
     */
    private void navigateToPopularServices() {
        if (getActivity() == null) {
            return;
        }

        PopularServiceFragment fragment = new PopularServiceFragment();
        getActivity().getSupportFragmentManager().beginTransaction()
                .replace(R.id.fragment_container, fragment)
                .addToBackStack(null)
                .commit();
    }

    /**
     * Navigate to Popular Items fragment
     */
    private void navigateToPopularItems() {
        if (getActivity() == null) {
            return;
        }

        PopularItemFragment fragment = new PopularItemFragment();
        getActivity().getSupportFragmentManager().beginTransaction()
                .replace(R.id.fragment_container, fragment)
                .addToBackStack(null)
                .commit();
    }

    /**
     * Show promotional dialog on every app launch
     */
    private void showPromoDialogOnAppLaunch() {
        // Check if this is an app launch (not internal navigation)
        Bundle args = getArguments();
        boolean isAppLaunch = args != null && args.getBoolean("isAppLaunch", false);

        android.util.Log.d("HomeFragment", "showPromoDialogOnAppLaunch called");
        android.util.Log.d("HomeFragment", "Arguments: " + args);
        android.util.Log.d("HomeFragment", "isAppLaunch: " + isAppLaunch);
        android.util.Log.d("HomeFragment", "promoDialogScheduled: " + promoDialogScheduled);

        if (isAppLaunch && !promoDialogScheduled) {
            android.util.Log.d("HomeFragment", "Scheduling promo dialog to show in " + (SHIMMER_DURATION + 1000) + "ms");
            promoDialogScheduled = true; // Set flag to prevent multiple scheduling

            // Create the task
            scheduledPromoTask = () -> {
                android.util.Log.d("HomeFragment", "=== PROMO TASK EXECUTING ===");
                try {
                    android.util.Log.d("HomeFragment", "Attempting to show promo dialog now");

                    // Try to find any active HomeFragment to show the dialog
                    if (getActivity() != null) {
                        android.util.Log.d("HomeFragment", "Activity is available");
                        Fragment currentFragment = getActivity().getSupportFragmentManager().findFragmentById(R.id.fragment_container);
                        android.util.Log.d("HomeFragment", "Current fragment: " + (currentFragment != null ? currentFragment.getClass().getSimpleName() : "null"));

                        if (currentFragment instanceof HomeFragment) {
                            HomeFragment activeHomeFragment = (HomeFragment) currentFragment;
                            android.util.Log.d("HomeFragment", "Found active HomeFragment, showing dialog");

                            PromoDialog promoDialog = PromoDialog.newInstance();
                            promoDialog.show(activeHomeFragment.getParentFragmentManager(), "PromoDialog");
                            android.util.Log.d("HomeFragment", "PromoDialog.show() completed");
                        } else {
                            android.util.Log.w("HomeFragment", "No active HomeFragment found - current fragment is: " +
                                (currentFragment != null ? currentFragment.getClass().getSimpleName() : "null"));
                        }
                    } else {
                        android.util.Log.w("HomeFragment", "No activity available");
                    }
                } catch (Exception e) {
                    // Handle dialog showing error with logging
                    android.util.Log.e("HomeFragment", "Error showing promo dialog: " + e.getMessage(), e);
                    e.printStackTrace();
                } finally {
                    // Reset flags after dialog is shown or failed
                    android.util.Log.d("HomeFragment", "=== PROMO TASK COMPLETED ===");
                    promoDialogScheduled = false;
                    scheduledPromoTask = null;
                }
            };

            // Delay showing the dialog until after data loads and shimmer effects complete
            // Use static handler to ensure it survives fragment recreation
            staticHandler.postDelayed(scheduledPromoTask, SHIMMER_DURATION + 1000); // Show 1 second after shimmer completes
            android.util.Log.d("HomeFragment", "Task posted to static handler");

            // Add a test task to verify the handler is working
            staticHandler.postDelayed(() -> {
                android.util.Log.d("HomeFragment", "TEST: Handler is working - this should appear in 1 second");
            }, 1000);
        } else if (promoDialogScheduled) {
            android.util.Log.d("HomeFragment", "Promo dialog already scheduled - skipping");
        } else {
            android.util.Log.d("HomeFragment", "Not showing promo dialog - not an app launch");
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        // Start all shimmer effects if they're visible
        if (recentOrdersShimmer != null && recentOrdersShimmer.getVisibility() == View.VISIBLE) {
            recentOrdersShimmer.startShimmer();
        }
        if (servicesShimmer != null && servicesShimmer.getVisibility() == View.VISIBLE) {
            servicesShimmer.startShimmer();
        }
        if (popularItemsShimmer != null && popularItemsShimmer.getVisibility() == View.VISIBLE) {
            popularItemsShimmer.startShimmer();
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        // Stop all shimmer effects to save battery
        if (recentOrdersShimmer != null) {
            recentOrdersShimmer.stopShimmer();
        }
        if (servicesShimmer != null) {
            servicesShimmer.stopShimmer();
        }
        if (popularItemsShimmer != null) {
            popularItemsShimmer.stopShimmer();
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        // Stop all shimmer effects and clean up resources
        if (recentOrdersShimmer != null) {
            recentOrdersShimmer.stopShimmer();
        }
        if (servicesShimmer != null) {
            servicesShimmer.stopShimmer();
        }
        if (popularItemsShimmer != null) {
            popularItemsShimmer.stopShimmer();
        }

        // Clean up handler callbacks to prevent memory leaks
        if (mainHandler != null) {
            mainHandler.removeCallbacksAndMessages(null);
        }
    }
}
