package com.mdsadrulhasan.gogolaundry.database.dao;

import android.database.Cursor;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.LiveData;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import com.mdsadrulhasan.gogolaundry.database.converters.DateConverter;
import com.mdsadrulhasan.gogolaundry.database.entity.UserEntity;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Integer;
import java.lang.Long;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class UserDao_Impl implements UserDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<UserEntity> __insertionAdapterOfUserEntity;

  private final EntityDeletionOrUpdateAdapter<UserEntity> __updateAdapterOfUserEntity;

  private final SharedSQLiteStatement __preparedStmtOfDeleteAll;

  public UserDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfUserEntity = new EntityInsertionAdapter<UserEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `users` (`id`,`fullName`,`phone`,`email`,`address`,`divisionId`,`districtId`,`upazillaId`,`isVerified`,`profilePictureUrl`,`createdAt`,`updatedAt`,`divisionName`,`districtName`,`upazillaName`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          final UserEntity entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getFullName() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getFullName());
        }
        if (entity.getPhone() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getPhone());
        }
        if (entity.getEmail() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getEmail());
        }
        if (entity.getAddress() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getAddress());
        }
        if (entity.getDivisionId() == null) {
          statement.bindNull(6);
        } else {
          statement.bindLong(6, entity.getDivisionId());
        }
        if (entity.getDistrictId() == null) {
          statement.bindNull(7);
        } else {
          statement.bindLong(7, entity.getDistrictId());
        }
        if (entity.getUpazillaId() == null) {
          statement.bindNull(8);
        } else {
          statement.bindLong(8, entity.getUpazillaId());
        }
        final int _tmp = entity.isVerified() ? 1 : 0;
        statement.bindLong(9, _tmp);
        if (entity.getProfilePictureUrl() == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, entity.getProfilePictureUrl());
        }
        final Long _tmp_1 = DateConverter.dateToTimestamp(entity.getCreatedAt());
        if (_tmp_1 == null) {
          statement.bindNull(11);
        } else {
          statement.bindLong(11, _tmp_1);
        }
        final Long _tmp_2 = DateConverter.dateToTimestamp(entity.getUpdatedAt());
        if (_tmp_2 == null) {
          statement.bindNull(12);
        } else {
          statement.bindLong(12, _tmp_2);
        }
        if (entity.getDivisionName() == null) {
          statement.bindNull(13);
        } else {
          statement.bindString(13, entity.getDivisionName());
        }
        if (entity.getDistrictName() == null) {
          statement.bindNull(14);
        } else {
          statement.bindString(14, entity.getDistrictName());
        }
        if (entity.getUpazillaName() == null) {
          statement.bindNull(15);
        } else {
          statement.bindString(15, entity.getUpazillaName());
        }
      }
    };
    this.__updateAdapterOfUserEntity = new EntityDeletionOrUpdateAdapter<UserEntity>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `users` SET `id` = ?,`fullName` = ?,`phone` = ?,`email` = ?,`address` = ?,`divisionId` = ?,`districtId` = ?,`upazillaId` = ?,`isVerified` = ?,`profilePictureUrl` = ?,`createdAt` = ?,`updatedAt` = ?,`divisionName` = ?,`districtName` = ?,`upazillaName` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          final UserEntity entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getFullName() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getFullName());
        }
        if (entity.getPhone() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getPhone());
        }
        if (entity.getEmail() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getEmail());
        }
        if (entity.getAddress() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getAddress());
        }
        if (entity.getDivisionId() == null) {
          statement.bindNull(6);
        } else {
          statement.bindLong(6, entity.getDivisionId());
        }
        if (entity.getDistrictId() == null) {
          statement.bindNull(7);
        } else {
          statement.bindLong(7, entity.getDistrictId());
        }
        if (entity.getUpazillaId() == null) {
          statement.bindNull(8);
        } else {
          statement.bindLong(8, entity.getUpazillaId());
        }
        final int _tmp = entity.isVerified() ? 1 : 0;
        statement.bindLong(9, _tmp);
        if (entity.getProfilePictureUrl() == null) {
          statement.bindNull(10);
        } else {
          statement.bindString(10, entity.getProfilePictureUrl());
        }
        final Long _tmp_1 = DateConverter.dateToTimestamp(entity.getCreatedAt());
        if (_tmp_1 == null) {
          statement.bindNull(11);
        } else {
          statement.bindLong(11, _tmp_1);
        }
        final Long _tmp_2 = DateConverter.dateToTimestamp(entity.getUpdatedAt());
        if (_tmp_2 == null) {
          statement.bindNull(12);
        } else {
          statement.bindLong(12, _tmp_2);
        }
        if (entity.getDivisionName() == null) {
          statement.bindNull(13);
        } else {
          statement.bindString(13, entity.getDivisionName());
        }
        if (entity.getDistrictName() == null) {
          statement.bindNull(14);
        } else {
          statement.bindString(14, entity.getDistrictName());
        }
        if (entity.getUpazillaName() == null) {
          statement.bindNull(15);
        } else {
          statement.bindString(15, entity.getUpazillaName());
        }
        statement.bindLong(16, entity.getId());
      }
    };
    this.__preparedStmtOfDeleteAll = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM users";
        return _query;
      }
    };
  }

  @Override
  public long insert(final UserEntity user) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      final long _result = __insertionAdapterOfUserEntity.insertAndReturnId(user);
      __db.setTransactionSuccessful();
      return _result;
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public void update(final UserEntity user) {
    __db.assertNotSuspendingTransaction();
    __db.beginTransaction();
    try {
      __updateAdapterOfUserEntity.handle(user);
      __db.setTransactionSuccessful();
    } finally {
      __db.endTransaction();
    }
  }

  @Override
  public void deleteAll() {
    __db.assertNotSuspendingTransaction();
    final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteAll.acquire();
    try {
      __db.beginTransaction();
      try {
        _stmt.executeUpdateDelete();
        __db.setTransactionSuccessful();
      } finally {
        __db.endTransaction();
      }
    } finally {
      __preparedStmtOfDeleteAll.release(_stmt);
    }
  }

  @Override
  public UserEntity getUserById(final int id) {
    final String _sql = "SELECT * FROM users WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, id);
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfFullName = CursorUtil.getColumnIndexOrThrow(_cursor, "fullName");
      final int _cursorIndexOfPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "phone");
      final int _cursorIndexOfEmail = CursorUtil.getColumnIndexOrThrow(_cursor, "email");
      final int _cursorIndexOfAddress = CursorUtil.getColumnIndexOrThrow(_cursor, "address");
      final int _cursorIndexOfDivisionId = CursorUtil.getColumnIndexOrThrow(_cursor, "divisionId");
      final int _cursorIndexOfDistrictId = CursorUtil.getColumnIndexOrThrow(_cursor, "districtId");
      final int _cursorIndexOfUpazillaId = CursorUtil.getColumnIndexOrThrow(_cursor, "upazillaId");
      final int _cursorIndexOfIsVerified = CursorUtil.getColumnIndexOrThrow(_cursor, "isVerified");
      final int _cursorIndexOfProfilePictureUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "profilePictureUrl");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
      final int _cursorIndexOfDivisionName = CursorUtil.getColumnIndexOrThrow(_cursor, "divisionName");
      final int _cursorIndexOfDistrictName = CursorUtil.getColumnIndexOrThrow(_cursor, "districtName");
      final int _cursorIndexOfUpazillaName = CursorUtil.getColumnIndexOrThrow(_cursor, "upazillaName");
      final UserEntity _result;
      if (_cursor.moveToFirst()) {
        _result = new UserEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _result.setId(_tmpId);
        final String _tmpFullName;
        if (_cursor.isNull(_cursorIndexOfFullName)) {
          _tmpFullName = null;
        } else {
          _tmpFullName = _cursor.getString(_cursorIndexOfFullName);
        }
        _result.setFullName(_tmpFullName);
        final String _tmpPhone;
        if (_cursor.isNull(_cursorIndexOfPhone)) {
          _tmpPhone = null;
        } else {
          _tmpPhone = _cursor.getString(_cursorIndexOfPhone);
        }
        _result.setPhone(_tmpPhone);
        final String _tmpEmail;
        if (_cursor.isNull(_cursorIndexOfEmail)) {
          _tmpEmail = null;
        } else {
          _tmpEmail = _cursor.getString(_cursorIndexOfEmail);
        }
        _result.setEmail(_tmpEmail);
        final String _tmpAddress;
        if (_cursor.isNull(_cursorIndexOfAddress)) {
          _tmpAddress = null;
        } else {
          _tmpAddress = _cursor.getString(_cursorIndexOfAddress);
        }
        _result.setAddress(_tmpAddress);
        final Integer _tmpDivisionId;
        if (_cursor.isNull(_cursorIndexOfDivisionId)) {
          _tmpDivisionId = null;
        } else {
          _tmpDivisionId = _cursor.getInt(_cursorIndexOfDivisionId);
        }
        _result.setDivisionId(_tmpDivisionId);
        final Integer _tmpDistrictId;
        if (_cursor.isNull(_cursorIndexOfDistrictId)) {
          _tmpDistrictId = null;
        } else {
          _tmpDistrictId = _cursor.getInt(_cursorIndexOfDistrictId);
        }
        _result.setDistrictId(_tmpDistrictId);
        final Integer _tmpUpazillaId;
        if (_cursor.isNull(_cursorIndexOfUpazillaId)) {
          _tmpUpazillaId = null;
        } else {
          _tmpUpazillaId = _cursor.getInt(_cursorIndexOfUpazillaId);
        }
        _result.setUpazillaId(_tmpUpazillaId);
        final boolean _tmpIsVerified;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsVerified);
        _tmpIsVerified = _tmp != 0;
        _result.setVerified(_tmpIsVerified);
        final String _tmpProfilePictureUrl;
        if (_cursor.isNull(_cursorIndexOfProfilePictureUrl)) {
          _tmpProfilePictureUrl = null;
        } else {
          _tmpProfilePictureUrl = _cursor.getString(_cursorIndexOfProfilePictureUrl);
        }
        _result.setProfilePictureUrl(_tmpProfilePictureUrl);
        final Date _tmpCreatedAt;
        final Long _tmp_1;
        if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
          _tmp_1 = null;
        } else {
          _tmp_1 = _cursor.getLong(_cursorIndexOfCreatedAt);
        }
        _tmpCreatedAt = DateConverter.fromTimestamp(_tmp_1);
        _result.setCreatedAt(_tmpCreatedAt);
        final Date _tmpUpdatedAt;
        final Long _tmp_2;
        if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
          _tmp_2 = null;
        } else {
          _tmp_2 = _cursor.getLong(_cursorIndexOfUpdatedAt);
        }
        _tmpUpdatedAt = DateConverter.fromTimestamp(_tmp_2);
        _result.setUpdatedAt(_tmpUpdatedAt);
        final String _tmpDivisionName;
        if (_cursor.isNull(_cursorIndexOfDivisionName)) {
          _tmpDivisionName = null;
        } else {
          _tmpDivisionName = _cursor.getString(_cursorIndexOfDivisionName);
        }
        _result.setDivisionName(_tmpDivisionName);
        final String _tmpDistrictName;
        if (_cursor.isNull(_cursorIndexOfDistrictName)) {
          _tmpDistrictName = null;
        } else {
          _tmpDistrictName = _cursor.getString(_cursorIndexOfDistrictName);
        }
        _result.setDistrictName(_tmpDistrictName);
        final String _tmpUpazillaName;
        if (_cursor.isNull(_cursorIndexOfUpazillaName)) {
          _tmpUpazillaName = null;
        } else {
          _tmpUpazillaName = _cursor.getString(_cursorIndexOfUpazillaName);
        }
        _result.setUpazillaName(_tmpUpazillaName);
      } else {
        _result = null;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @Override
  public LiveData<UserEntity> getUserByIdLive(final int id) {
    final String _sql = "SELECT * FROM users WHERE id = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    _statement.bindLong(_argIndex, id);
    return __db.getInvalidationTracker().createLiveData(new String[] {"users"}, false, new Callable<UserEntity>() {
      @Override
      @Nullable
      public UserEntity call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfFullName = CursorUtil.getColumnIndexOrThrow(_cursor, "fullName");
          final int _cursorIndexOfPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "phone");
          final int _cursorIndexOfEmail = CursorUtil.getColumnIndexOrThrow(_cursor, "email");
          final int _cursorIndexOfAddress = CursorUtil.getColumnIndexOrThrow(_cursor, "address");
          final int _cursorIndexOfDivisionId = CursorUtil.getColumnIndexOrThrow(_cursor, "divisionId");
          final int _cursorIndexOfDistrictId = CursorUtil.getColumnIndexOrThrow(_cursor, "districtId");
          final int _cursorIndexOfUpazillaId = CursorUtil.getColumnIndexOrThrow(_cursor, "upazillaId");
          final int _cursorIndexOfIsVerified = CursorUtil.getColumnIndexOrThrow(_cursor, "isVerified");
          final int _cursorIndexOfProfilePictureUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "profilePictureUrl");
          final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
          final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
          final int _cursorIndexOfDivisionName = CursorUtil.getColumnIndexOrThrow(_cursor, "divisionName");
          final int _cursorIndexOfDistrictName = CursorUtil.getColumnIndexOrThrow(_cursor, "districtName");
          final int _cursorIndexOfUpazillaName = CursorUtil.getColumnIndexOrThrow(_cursor, "upazillaName");
          final UserEntity _result;
          if (_cursor.moveToFirst()) {
            _result = new UserEntity();
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            _result.setId(_tmpId);
            final String _tmpFullName;
            if (_cursor.isNull(_cursorIndexOfFullName)) {
              _tmpFullName = null;
            } else {
              _tmpFullName = _cursor.getString(_cursorIndexOfFullName);
            }
            _result.setFullName(_tmpFullName);
            final String _tmpPhone;
            if (_cursor.isNull(_cursorIndexOfPhone)) {
              _tmpPhone = null;
            } else {
              _tmpPhone = _cursor.getString(_cursorIndexOfPhone);
            }
            _result.setPhone(_tmpPhone);
            final String _tmpEmail;
            if (_cursor.isNull(_cursorIndexOfEmail)) {
              _tmpEmail = null;
            } else {
              _tmpEmail = _cursor.getString(_cursorIndexOfEmail);
            }
            _result.setEmail(_tmpEmail);
            final String _tmpAddress;
            if (_cursor.isNull(_cursorIndexOfAddress)) {
              _tmpAddress = null;
            } else {
              _tmpAddress = _cursor.getString(_cursorIndexOfAddress);
            }
            _result.setAddress(_tmpAddress);
            final Integer _tmpDivisionId;
            if (_cursor.isNull(_cursorIndexOfDivisionId)) {
              _tmpDivisionId = null;
            } else {
              _tmpDivisionId = _cursor.getInt(_cursorIndexOfDivisionId);
            }
            _result.setDivisionId(_tmpDivisionId);
            final Integer _tmpDistrictId;
            if (_cursor.isNull(_cursorIndexOfDistrictId)) {
              _tmpDistrictId = null;
            } else {
              _tmpDistrictId = _cursor.getInt(_cursorIndexOfDistrictId);
            }
            _result.setDistrictId(_tmpDistrictId);
            final Integer _tmpUpazillaId;
            if (_cursor.isNull(_cursorIndexOfUpazillaId)) {
              _tmpUpazillaId = null;
            } else {
              _tmpUpazillaId = _cursor.getInt(_cursorIndexOfUpazillaId);
            }
            _result.setUpazillaId(_tmpUpazillaId);
            final boolean _tmpIsVerified;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsVerified);
            _tmpIsVerified = _tmp != 0;
            _result.setVerified(_tmpIsVerified);
            final String _tmpProfilePictureUrl;
            if (_cursor.isNull(_cursorIndexOfProfilePictureUrl)) {
              _tmpProfilePictureUrl = null;
            } else {
              _tmpProfilePictureUrl = _cursor.getString(_cursorIndexOfProfilePictureUrl);
            }
            _result.setProfilePictureUrl(_tmpProfilePictureUrl);
            final Date _tmpCreatedAt;
            final Long _tmp_1;
            if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
              _tmp_1 = null;
            } else {
              _tmp_1 = _cursor.getLong(_cursorIndexOfCreatedAt);
            }
            _tmpCreatedAt = DateConverter.fromTimestamp(_tmp_1);
            _result.setCreatedAt(_tmpCreatedAt);
            final Date _tmpUpdatedAt;
            final Long _tmp_2;
            if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
              _tmp_2 = null;
            } else {
              _tmp_2 = _cursor.getLong(_cursorIndexOfUpdatedAt);
            }
            _tmpUpdatedAt = DateConverter.fromTimestamp(_tmp_2);
            _result.setUpdatedAt(_tmpUpdatedAt);
            final String _tmpDivisionName;
            if (_cursor.isNull(_cursorIndexOfDivisionName)) {
              _tmpDivisionName = null;
            } else {
              _tmpDivisionName = _cursor.getString(_cursorIndexOfDivisionName);
            }
            _result.setDivisionName(_tmpDivisionName);
            final String _tmpDistrictName;
            if (_cursor.isNull(_cursorIndexOfDistrictName)) {
              _tmpDistrictName = null;
            } else {
              _tmpDistrictName = _cursor.getString(_cursorIndexOfDistrictName);
            }
            _result.setDistrictName(_tmpDistrictName);
            final String _tmpUpazillaName;
            if (_cursor.isNull(_cursorIndexOfUpazillaName)) {
              _tmpUpazillaName = null;
            } else {
              _tmpUpazillaName = _cursor.getString(_cursorIndexOfUpazillaName);
            }
            _result.setUpazillaName(_tmpUpazillaName);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
        }
      }

      @Override
      protected void finalize() {
        _statement.release();
      }
    });
  }

  @Override
  public UserEntity getUserByPhone(final String phone) {
    final String _sql = "SELECT * FROM users WHERE phone = ?";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 1);
    int _argIndex = 1;
    if (phone == null) {
      _statement.bindNull(_argIndex);
    } else {
      _statement.bindString(_argIndex, phone);
    }
    __db.assertNotSuspendingTransaction();
    final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
    try {
      final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
      final int _cursorIndexOfFullName = CursorUtil.getColumnIndexOrThrow(_cursor, "fullName");
      final int _cursorIndexOfPhone = CursorUtil.getColumnIndexOrThrow(_cursor, "phone");
      final int _cursorIndexOfEmail = CursorUtil.getColumnIndexOrThrow(_cursor, "email");
      final int _cursorIndexOfAddress = CursorUtil.getColumnIndexOrThrow(_cursor, "address");
      final int _cursorIndexOfDivisionId = CursorUtil.getColumnIndexOrThrow(_cursor, "divisionId");
      final int _cursorIndexOfDistrictId = CursorUtil.getColumnIndexOrThrow(_cursor, "districtId");
      final int _cursorIndexOfUpazillaId = CursorUtil.getColumnIndexOrThrow(_cursor, "upazillaId");
      final int _cursorIndexOfIsVerified = CursorUtil.getColumnIndexOrThrow(_cursor, "isVerified");
      final int _cursorIndexOfProfilePictureUrl = CursorUtil.getColumnIndexOrThrow(_cursor, "profilePictureUrl");
      final int _cursorIndexOfCreatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "createdAt");
      final int _cursorIndexOfUpdatedAt = CursorUtil.getColumnIndexOrThrow(_cursor, "updatedAt");
      final int _cursorIndexOfDivisionName = CursorUtil.getColumnIndexOrThrow(_cursor, "divisionName");
      final int _cursorIndexOfDistrictName = CursorUtil.getColumnIndexOrThrow(_cursor, "districtName");
      final int _cursorIndexOfUpazillaName = CursorUtil.getColumnIndexOrThrow(_cursor, "upazillaName");
      final UserEntity _result;
      if (_cursor.moveToFirst()) {
        _result = new UserEntity();
        final int _tmpId;
        _tmpId = _cursor.getInt(_cursorIndexOfId);
        _result.setId(_tmpId);
        final String _tmpFullName;
        if (_cursor.isNull(_cursorIndexOfFullName)) {
          _tmpFullName = null;
        } else {
          _tmpFullName = _cursor.getString(_cursorIndexOfFullName);
        }
        _result.setFullName(_tmpFullName);
        final String _tmpPhone;
        if (_cursor.isNull(_cursorIndexOfPhone)) {
          _tmpPhone = null;
        } else {
          _tmpPhone = _cursor.getString(_cursorIndexOfPhone);
        }
        _result.setPhone(_tmpPhone);
        final String _tmpEmail;
        if (_cursor.isNull(_cursorIndexOfEmail)) {
          _tmpEmail = null;
        } else {
          _tmpEmail = _cursor.getString(_cursorIndexOfEmail);
        }
        _result.setEmail(_tmpEmail);
        final String _tmpAddress;
        if (_cursor.isNull(_cursorIndexOfAddress)) {
          _tmpAddress = null;
        } else {
          _tmpAddress = _cursor.getString(_cursorIndexOfAddress);
        }
        _result.setAddress(_tmpAddress);
        final Integer _tmpDivisionId;
        if (_cursor.isNull(_cursorIndexOfDivisionId)) {
          _tmpDivisionId = null;
        } else {
          _tmpDivisionId = _cursor.getInt(_cursorIndexOfDivisionId);
        }
        _result.setDivisionId(_tmpDivisionId);
        final Integer _tmpDistrictId;
        if (_cursor.isNull(_cursorIndexOfDistrictId)) {
          _tmpDistrictId = null;
        } else {
          _tmpDistrictId = _cursor.getInt(_cursorIndexOfDistrictId);
        }
        _result.setDistrictId(_tmpDistrictId);
        final Integer _tmpUpazillaId;
        if (_cursor.isNull(_cursorIndexOfUpazillaId)) {
          _tmpUpazillaId = null;
        } else {
          _tmpUpazillaId = _cursor.getInt(_cursorIndexOfUpazillaId);
        }
        _result.setUpazillaId(_tmpUpazillaId);
        final boolean _tmpIsVerified;
        final int _tmp;
        _tmp = _cursor.getInt(_cursorIndexOfIsVerified);
        _tmpIsVerified = _tmp != 0;
        _result.setVerified(_tmpIsVerified);
        final String _tmpProfilePictureUrl;
        if (_cursor.isNull(_cursorIndexOfProfilePictureUrl)) {
          _tmpProfilePictureUrl = null;
        } else {
          _tmpProfilePictureUrl = _cursor.getString(_cursorIndexOfProfilePictureUrl);
        }
        _result.setProfilePictureUrl(_tmpProfilePictureUrl);
        final Date _tmpCreatedAt;
        final Long _tmp_1;
        if (_cursor.isNull(_cursorIndexOfCreatedAt)) {
          _tmp_1 = null;
        } else {
          _tmp_1 = _cursor.getLong(_cursorIndexOfCreatedAt);
        }
        _tmpCreatedAt = DateConverter.fromTimestamp(_tmp_1);
        _result.setCreatedAt(_tmpCreatedAt);
        final Date _tmpUpdatedAt;
        final Long _tmp_2;
        if (_cursor.isNull(_cursorIndexOfUpdatedAt)) {
          _tmp_2 = null;
        } else {
          _tmp_2 = _cursor.getLong(_cursorIndexOfUpdatedAt);
        }
        _tmpUpdatedAt = DateConverter.fromTimestamp(_tmp_2);
        _result.setUpdatedAt(_tmpUpdatedAt);
        final String _tmpDivisionName;
        if (_cursor.isNull(_cursorIndexOfDivisionName)) {
          _tmpDivisionName = null;
        } else {
          _tmpDivisionName = _cursor.getString(_cursorIndexOfDivisionName);
        }
        _result.setDivisionName(_tmpDivisionName);
        final String _tmpDistrictName;
        if (_cursor.isNull(_cursorIndexOfDistrictName)) {
          _tmpDistrictName = null;
        } else {
          _tmpDistrictName = _cursor.getString(_cursorIndexOfDistrictName);
        }
        _result.setDistrictName(_tmpDistrictName);
        final String _tmpUpazillaName;
        if (_cursor.isNull(_cursorIndexOfUpazillaName)) {
          _tmpUpazillaName = null;
        } else {
          _tmpUpazillaName = _cursor.getString(_cursorIndexOfUpazillaName);
        }
        _result.setUpazillaName(_tmpUpazillaName);
      } else {
        _result = null;
      }
      return _result;
    } finally {
      _cursor.close();
      _statement.release();
    }
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
